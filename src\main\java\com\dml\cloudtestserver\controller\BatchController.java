package com.dml.cloudtestserver.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.dml.cloudtestserver.model.batch.BatchReq;
import com.dml.cloudtestserver.service.BatchService;
import com.dml.cloudtestserver.util.constants.BatchStatusEnum;
import com.dml.cloudtestserver.util.constants.Constants;
import com.dml.cloudtestserver.util.response.ResponseResult;
import com.dml.cloudtestserver.util.validator.ValidEnumList;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;


@Tag(name = "批次管理")
@RestController
@Validated
@RequestMapping("/api/batch")
public class BatchController {

    @Autowired
    private BatchService batchService;


    @Operation(summary = "添加批次")
    @PostMapping("")
    @SaCheckPermission("batch-add")
    public ResponseResult<?> addBatch(
            @Schema(implementation = BatchReq.class)
            @Valid
            @RequestBody
            BatchReq batchReq) {
        return batchService.addBatch(batchReq);
    }


    @Operation(summary = "删除批次")
    @DeleteMapping("/{uid}")
    @SaCheckPermission("batch-delete")
    public ResponseResult<?> deleteBatch(
            @PathVariable
            Long uid) {
        return batchService.deleteBatch(uid);
    }


    @Operation(summary = "更改批次")
    @PutMapping("/{uid}")
    @SaCheckPermission("batch-update")
    public ResponseResult<?> updateBatch(
            @PathVariable
            Long uid,

            @Schema(implementation = BatchReq.class)
            @Valid
            @RequestBody
            BatchReq batchReq) {
        return batchService.updateBatch(uid, batchReq);
    }


//    @Operation(summary = "查询单个批次")
//    @GetMapping("/{uid}")
//    @SaCheckPermission("batch-query")
//    public ResponseResult<?> queryBatch(@PathVariable Long uid) {
//        return batchService.queryBatch(uid);
//    }


    @Operation(summary = "批量查询批次")
    @GetMapping("")
    @SaCheckPermission("batch-query")
    public ResponseResult<?> queryBatchList(
            @Parameter(description = "页码")
            @RequestParam(value = "pageNum", defaultValue = Constants.DEFAULT_PAGE_NUM)
            Integer pageNum,

            @Parameter(description = "每页数量")
            @RequestParam(value = "pageSize", defaultValue = Constants.DEFAULT_PAGE_SIZE)
            Integer pageSize,

            @Parameter(description = "批次号")
            @RequestParam(value = "batchId", required = false)
            String batchId,

            @Parameter(description = "状态")
            @RequestParam(value = "status", required = false)
            @ValidEnumList(enumClass = BatchStatusEnum.class, message = "可选值：1-未开始(默认)、2-运行中、4-已停止")
            List<Integer> status
//            @Parameter(description = "起始创建时间 格式：yyyy-MM-dd HH:mm:ss") @RequestParam(value = "startCreatedTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startCreatedTime,
//            @Parameter(description = "结束创建时间 格式：yyyy-MM-dd HH:mm:ss") @RequestParam(value = "endCreatedTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endCreatedTime
    ) {
        // 时间逻辑检查
//        if (startCreatedTime != null && endCreatedTime != null && startCreatedTime.after(endCreatedTime)) {
//            return ResponseResult.error(ResponseEnum.PARAMETER_VALIDATION_ERROR, "起始创建时间不能晚于结束创建时间");
//        }
        return batchService.queryBatchList(pageNum, pageSize, batchId, status, null, null);
    }


    @Operation(summary = "查询所有批次")
    @GetMapping("/all")
    @SaCheckPermission("batch-query")
    public ResponseResult<?> queryAllBatch(
            @Parameter(description = "状态")
            @RequestParam(value = "status", required = false)
            @ValidEnumList(enumClass = BatchStatusEnum.class, message = "可选值：1-未开始(默认)、2-运行中、4-已停止")
            List<Integer> status) {
        return batchService.queryAllBatch(status);
    }


}
