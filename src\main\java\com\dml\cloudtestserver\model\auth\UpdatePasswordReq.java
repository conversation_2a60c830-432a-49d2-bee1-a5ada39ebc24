package com.dml.cloudtestserver.model.auth;

import com.dml.cloudtestserver.util.constants.Constants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UpdatePasswordReq implements Serializable {

    private static final long serialVersionUID = 8247362466966683979L;


    /**
     * 密码
     */
    @Schema(description = "旧密码")
    @NotBlank(message = "旧密码必填")
    private String oldPassword;


    /**
     * 新密码
     */
    @Schema(description = "新密码。仅支持字母、数字、下划线, 长度1-20")
    @NotBlank(message = "新密码必填")
    @Length(min = 1, max = 20)
    @Pattern(regexp = Constants.REGEX_LETTER_NUMBER_UNDERLINE, message = Constants.REGEX_LETTER_NUMBER_UNDERLINE_MSG)
    private String newPassword;
}
