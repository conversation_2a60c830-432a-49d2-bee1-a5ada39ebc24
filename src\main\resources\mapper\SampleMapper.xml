<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dml.cloudtestserver.dao.SampleDao">


    <!-- 结果映射，将数据库字段映射到 SampleEntity 对象的属性 -->
    <resultMap id="BaseResultMap" type="com.dml.cloudtestserver.model.sample.SampleEntity">
        <id column="uid" property="uid"/>
        <result column="batch_uid" property="batchUid"/>
        <result column="sample_id" property="sampleId"/>
        <result column="flash_type" property="flashType"/>
        <result column="flash_num" property="flashNum"/>
        <result column="product_type" property="productType"/>
        <result column="controller_type" property="controllerType"/>
        <result column="interface_type" property="interfaceType"/>
        <result column="capacity" property="capacity"/>
        <result column="capacity_mode" property="capacityMode"/>
        <result column="speed_type" property="speedType"/>
        <result column="production_mode" property="productionMode"/>
        <result column="production_version" property="productionVersion"/>
        <result column="production_result" property="productionResult"/>
        <result column="error_code" property="errorCode"/>
        <result column="os_platform" property="osPlatform"/>
        <result column="log_dir" property="logDir"/>
        <result column="log_name" property="logName"/>
        <result column="log_upload_status" property="logUploadStatus"/>
        <result column="created_time" property="createdTime"/>
    </resultMap>


    <select id="existsSampleBySampleId" resultType="boolean">
        SELECT EXISTS (SELECT 1
                       FROM ${tbSampleName}
                       WHERE sample_id = #{sampleId})
    </select>


    <insert id="insertSample" parameterType="map">
        INSERT INTO ${tbSampleName} (uid, batch_uid, sample_id, flash_type, flash_num, product_type,
                                     controller_type, interface_type, capacity, capacity_mode, speed_type,
                                     production_mode, production_version, production_result, error_code,
                                     os_platform, log_dir, log_name, log_upload_status)
        VALUES (#{sampleInsertDto.uid}, #{sampleInsertDto.batchUid}, #{sampleInsertDto.sampleId},
                #{sampleInsertDto.flashType}, #{sampleInsertDto.flashNum}, #{sampleInsertDto.productType},
                #{sampleInsertDto.controllerType}, #{sampleInsertDto.interfaceType}, #{sampleInsertDto.capacity},
                #{sampleInsertDto.capacityMode}, #{sampleInsertDto.speedType},
                #{sampleInsertDto.productionMode}, #{sampleInsertDto.productionVersion},
                #{sampleInsertDto.productionResult}, #{sampleInsertDto.errorCode},
                #{sampleInsertDto.osPlatform}, #{sampleInsertDto.logDir}, #{sampleInsertDto.logName},
                #{sampleInsertDto.logUploadStatus})
    </insert>


    <update id="updateSample" parameterType="map">
        UPDATE ${tbSampleName}
        SET log_upload_status = #{sampleUpdateDto.logUploadStatus}
        WHERE uid = #{sampleUpdateDto.uid}
    </update>


    <select id="selectSample" resultMap="BaseResultMap"
            parameterType="Long">
        SELECT uid,
               batch_uid,
               sample_id,
               flash_type,
               flash_num,
               product_type,
               controller_type,
               interface_type,
               capacity,
               capacity_mode,
               speed_type,
               production_mode,
               production_version,
               production_result,
               error_code,
               os_platform,
               log_dir,
               log_name,
               log_upload_status,
               created_time
        FROM ${tbSampleName}
        WHERE uid = #{uid}
    </select>


    <select id="selectSampleList" resultMap="BaseResultMap">
        SELECT
        uid, batch_uid, sample_id, flash_type, flash_num, product_type,
        controller_type, interface_type, capacity, capacity_mode, speed_type,
        production_mode, production_version, production_result, error_code,
        os_platform, log_dir, log_name, log_upload_status, created_time
        FROM ${tbSampleName}
        <where>
            <if test="sampleId != null and sampleId != ''">
                AND sample_id LIKE CONCAT('%', #{sampleId}, '%')
            </if>
            <if test="true">
                <if test="productType != null and productType.size() > 0">
                    AND (product_type IN
                    <foreach item="item" index="index" collection="productType" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    )
                </if>
                <if test="productType == null or productType.size() == 0">
                    AND (product_type IS NULL OR product_type = '')
                </if>
            </if>
            <if test="true">
                <if test="productionResult != null and productionResult.size() > 0">
                    AND (production_result IN
                    <foreach item="item" index="index" collection="productionResult" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    )
                </if>
                <if test="productionResult == null or productionResult.size() == 0">
                    AND (production_result IS NULL OR production_result = '')
                </if>
            </if>
            <if test="true">
                <if test="logUploadStatus != null and logUploadStatus.size() > 0">
                    AND (log_upload_status IN
                    <foreach item="item" index="index" collection="logUploadStatus" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    )
                </if>
                <if test="logUploadStatus == null or logUploadStatus.size() == 0">
                    AND (log_upload_status IS NULL OR log_upload_status = '')
                </if>
            </if>
            <if test="startCreatedTime != null">
                AND created_time &gt;= #{startCreatedTime}
            </if>
            <if test="endCreatedTime != null">
                AND created_time &lt;= #{endCreatedTime}
            </if>
        </where>
    </select>


    <select id="selectAllSampleId" resultType="com.dml.cloudtestserver.model.sample.SampleIdRes">
        SELECT DISTINCT sample_id AS sampleId
        FROM ${tbSampleName}
    </select>


    <select id="countSampleNum" resultType="int">
        SELECT COUNT(DISTINCT sample_id)
        FROM ${tbSampleName}
    </select>


    <select id="countHourlySampleNum" parameterType="String"
            resultType="com.dml.cloudtestserver.model.sample.PerHourSampleCountDto">
        SELECT HOUR(created_time)        AS hour,
               COUNT(DISTINCT sample_id) AS sampleCount
        FROM ${tbSampleName}
        WHERE DATE(created_time) = CURDATE() -- 过滤当天的记录
        GROUP BY HOUR(created_time)
        ORDER BY hour;
    </select>


    <select id="countHourlySampleSuccessRate" parameterType="String"
            resultType="com.dml.cloudtestserver.model.sample.PerHourSuccessRateDto">
        #         SELECT HOUR(created_time)                                                            AS hour,
#                COUNT(CASE WHEN production_result = 1 THEN 1 END) / COUNT(DISTINCT sample_id) AS successRate
#         FROM (SELECT sample_id, production_result, created_time
#               FROM ${tbSampleName}
#               WHERE DATE(created_time) = CURDATE() -- 过滤当天的记录
#                 AND (sample_id, created_time) IN (SELECT sample_id, MAX(created_time) AS latest_time
#                                                   FROM ${tbSampleName}
#                                                   WHERE DATE(created_time) = CURDATE()
#                                                   GROUP BY sample_id)) AS latest_samples
#         GROUP BY hour
#         ORDER BY hour;


#         SELECT HOUR(t.created_time)                                                                   AS hour,
#                SUM(CASE WHEN t.production_result = 1 THEN 1 ELSE 0 END) / COUNT(DISTINCT t.sample_id) AS successRate
#         FROM ${tbSampleName} t
#                  JOIN (SELECT sample_id, MAX(created_time) AS latest_time
#                        FROM ${tbSampleName}
#                        WHERE DATE(created_time) = CURDATE()
#                        GROUP BY sample_id) AS latest_samples
#                       ON t.sample_id = latest_samples.sample_id AND t.created_time = latest_samples.latest_time
#         WHERE DATE(t.created_time) = CURDATE()
#         GROUP BY hour
#         ORDER BY hour;


        WITH LatestSamples AS (SELECT sample_id,
                                      production_result,
                                      created_time,
                                      ROW_NUMBER() OVER (PARTITION BY sample_id ORDER BY created_time DESC) AS rn
                               FROM ${tbSampleName}
                               WHERE DATE(created_time) = CURDATE())
        SELECT HOUR(created_time)                                               AS hour,
               ROUND(SUM(production_result = 1) / COUNT(DISTINCT sample_id), 4) AS successRate
        FROM LatestSamples
        WHERE rn = 1
        GROUP BY hour
        ORDER BY hour;
    </select>


    <select id="countSampleNumByCapacity" parameterType="String"
            resultType="com.dml.cloudtestserver.model.sample.PerCapacitySampleCountDto">
        # 子查询
        # SELECT CASE
        # WHEN (capacity / 1024) &lt; 2 THEN '2GB'
        # WHEN (capacity / 1024) &gt;= 2 AND (capacity / 1024) &lt; 4 THEN '4GB'
        # WHEN (capacity / 1024) &gt;= 4 AND (capacity / 1024) &lt; 8 THEN '8GB'
        # WHEN (capacity / 1024) &gt;= 8 AND (capacity / 1024) &lt; 16 THEN '16GB'
        # WHEN (capacity / 1024) &gt;= 16 AND (capacity / 1024) &lt; 32 THEN '32GB'
        # WHEN (capacity / 1024) &gt;= 32 AND (capacity / 1024) &lt; 64 THEN '64GB'
        # WHEN (capacity / 1024) &gt;= 64 AND (capacity / 1024) &lt; 128 THEN '128GB'
        # WHEN (capacity / 1024) &gt;= 128 AND (capacity / 1024) &lt; 256 THEN '256GB'
        # WHEN (capacity / 1024) &gt;= 256 AND (capacity / 1024) &lt; 512 THEN '512GB'
        # WHEN (capacity / 1024) &gt;= 512 AND (capacity / 1024) &lt; 1024 THEN '1024GB'
        # ELSE 'other'
        # END AS capacity,
        # COUNT(DISTINCT sample_id) AS count
        # FROM (SELECT sample_id, capacity
        # FROM ${tbSampleName}
        # WHERE DATE(created_time) = CURDATE() -- 过滤当天的记录
        # AND (sample_id, created_time) IN (SELECT sample_id, MAX(created_time) AS latest_time
        # FROM ${tbSampleName}
        # WHERE DATE(created_time) = CURDATE()
        # GROUP BY sample_id)) AS latest_samples
        # GROUP BY capacity_range
        # ORDER BY FIELD(capacity_range, '2GB', '4GB', '8GB', '16GB', '32GB', '64GB', '128GB', '256GB', '512GB',
        # '1024GB',
        # 'other');

        # JOIN 连接
        # SELECT CASE
        # WHEN (t.capacity / 1024) &lt; 2 THEN '2GB'
        # WHEN (t.capacity / 1024) &gt;= 2 AND (t.capacity / 1024) &lt; 4 THEN '4GB'
        # WHEN (t.capacity / 1024) &gt;= 4 AND (t.capacity / 1024) &lt; 8 THEN '8GB'
        # WHEN (t.capacity / 1024) &gt;= 8 AND (t.capacity / 1024) &lt; 16 THEN '16GB'
        # WHEN (t.capacity / 1024) &gt;= 16 AND (t.capacity / 1024) &lt; 32 THEN '32GB'
        # WHEN (t.capacity / 1024) &gt;= 32 AND (t.capacity / 1024) &lt; 64 THEN '64GB'
        # WHEN (t.capacity / 1024) &gt;= 64 AND (t.capacity / 1024) &lt; 128 THEN '128GB'
        # WHEN (t.capacity / 1024) &gt;= 128 AND (t.capacity / 1024) &lt; 256 THEN '256GB'
        # WHEN (t.capacity / 1024) &gt;= 256 AND (t.capacity / 1024) &lt; 512 THEN '512GB'
        # WHEN (t.capacity / 1024) &gt;= 512 AND (t.capacity / 1024) &lt; 1024 THEN '1024GB'
        # ELSE 'other'
        # END AS capacity_range,
        # COUNT(DISTINCT t.sample_id) AS sample_count
        # FROM db_cloud_test.tb_sample_a t
        # JOIN (SELECT sample_id, MAX(created_time) AS latest_time
        # FROM ${tbSampleName}
        # WHERE DATE(created_time) = CURDATE()
        # GROUP BY sample_id) latest
        # ON t.sample_id = latest.sample_id AND t.created_time = latest.latest_time
        # WHERE DATE(t.created_time) = CURDATE()
        # GROUP BY capacity_range
        # ORDER BY FIELD(capacity_range, '2GB', '4GB', '8GB', '16GB', '32GB', '64GB', '128GB', '256GB', '512GB',
        # '1024GB',
        # 'other');

        # 窗口函数
        WITH LatestSamples AS (
        SELECT t.*,
        ROW_NUMBER() OVER (PARTITION BY t.sample_id ORDER BY t.created_time DESC) AS rn
        FROM ${tbSampleName} t  <!-- t 是原始表的别名 -->
        WHERE DATE(t.created_time) = CURDATE()
        )
        SELECT CASE
        WHEN (t.capacity / 1024) &lt; 2 THEN '2GB'
        WHEN (t.capacity / 1024) &gt;= 2 AND (t.capacity / 1024) &lt; 4 THEN '4GB'
        WHEN (t.capacity / 1024) &gt;= 4 AND (t.capacity / 1024) &lt; 8 THEN '8GB'
        WHEN (t.capacity / 1024) &gt;= 8 AND (t.capacity / 1024) &lt; 16 THEN '16GB'
        WHEN (t.capacity / 1024) &gt;= 16 AND (t.capacity / 1024) &lt; 32 THEN '32GB'
        WHEN (t.capacity / 1024) &gt;= 32 AND (t.capacity / 1024) &lt; 64 THEN '64GB'
        WHEN (t.capacity / 1024) &gt;= 64 AND (t.capacity / 1024) &lt; 128 THEN '128GB'
        WHEN (t.capacity / 1024) &gt;= 128 AND (t.capacity / 1024) &lt; 256 THEN '256GB'
        WHEN (t.capacity / 1024) &gt;= 256 AND (t.capacity / 1024) &lt; 512 THEN '512GB'
        WHEN (t.capacity / 1024) &gt;= 512 AND (t.capacity / 1024) &lt; 1024 THEN '1024GB'
        ELSE 'other'
        END AS capacityRange,
        COUNT(DISTINCT t.sample_id) AS sampleCount
        FROM LatestSamples t  <!-- 使用正确的别名 LatestSamples -->
        WHERE t.rn = 1
        GROUP BY capacityRange
        ORDER BY FIELD(capacityRange, '2GB', '4GB', '8GB', '16GB', '32GB', '64GB', '128GB', '256GB', '512GB', '1024GB',
        'other');
    </select>


</mapper>