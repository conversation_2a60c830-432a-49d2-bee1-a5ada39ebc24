package com.dml.cloudtestserver.model.permission;

import com.dml.cloudtestserver.util.constants.Constants;
import com.dml.cloudtestserver.util.constants.PermissionScopeEnum;
import com.dml.cloudtestserver.util.tool.LongDeserializer;
import com.dml.cloudtestserver.util.validator.ValidEnum;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PermissionReq implements Serializable {

    private static final long serialVersionUID = 5083751647136249913L;

    /**
     * 权限范围。1-目录, 2-路由, 3-功能
     */
    @Schema(description = "权限范围。可选值: 1-目录, 2-路由, 3-功能")
    @NotNull(message = "权限范围必填")
    @ValidEnum(enumClass = PermissionScopeEnum.class, message = "可选值: 1-目录, 2-路由, 3-功能")
    private Integer scope;

    /**
     * 父权限UID
     */
    @Schema(description = "父权限UID")
    @JsonDeserialize(using = LongDeserializer.class)
    private Long parentUid = 1L;

    /**
     * 权限名称
     */
    @Schema(description = "权限名称。由中文、字母、数字、下划线组成（下划线不能开头或结尾或仅有下划线）, 长度1-50")
    @NotBlank(message = "权限名称必填")
    @Length(min = 1, max = 50)
    @Pattern(regexp = Constants.REGEX_CHINESE_LETTER_NUMBER_UNDERLINE, message = Constants.REGEX_CHINESE_LETTER_NUMBER_UNDERLINE_MSG)
    private String name;

    /**
     * 权限码
     */
    @Schema(description = "权限码。由字母、数字、短横线组成（短横线不能开头或结尾或仅有短横线）, 长度1-50")
    @Length(min = 1, max = 50)
    @Pattern(regexp = Constants.REGEX_LETTER_NUMBER_DASH, message = Constants.REGEX_LETTER_NUMBER_DASH_MSG)
    private String code;

    /**
     * 排序权重
     */
    @Schema(description = "排序权重。必须>=0, 默认为0")
    @Min(value = 0, message = "排序权重必须>=0")
    @NotNull(message = "排序权重必填")
    private Integer sortOrder;
}
