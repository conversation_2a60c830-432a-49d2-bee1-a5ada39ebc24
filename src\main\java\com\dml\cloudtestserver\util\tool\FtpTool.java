package com.dml.cloudtestserver.util.tool;

import com.dml.cloudtestserver.util.properties.FtpProperties;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.commons.net.ftp.FTPReply;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;


@Component
public class FtpTool {

    @Autowired
    private FtpProperties ftpProperties;

    private FTPClient ftpClient;

    public FtpTool() {
        this.ftpClient = new FTPClient();
    }

    /**
     * 连接到 FTP 服务器
     */
    public boolean connect() {
        try {
            ftpClient.connect(ftpProperties.getHost(), ftpProperties.getPort());
            ftpClient.login(ftpProperties.getAccount(), ftpProperties.getPassword());
            ftpClient.enterLocalPassiveMode();

            // 检查是否成功连接
            return FTPReply.isPositiveCompletion(ftpClient.getReplyCode());
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 断开与 FTP 服务器的连接
     */
    public void disconnect() {
        if (ftpClient.isConnected()) {
            try {
                ftpClient.logout();
                ftpClient.disconnect();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 检查指定的 FTP 目录是否存在
     *
     * @param dirPath 目录路径
     * @return 如果存在返回 true，否则返回 false
     */
    public boolean checkDirectoryExists(String dirPath) {
        try {
            // 切换到指定目录
            ftpClient.changeWorkingDirectory(dirPath);
            return true; // 如果能切换到该目录，表示存在
        } catch (IOException e) {
            // 如果出现异常，表示目录不存在
            return false;
        }
    }

    /**
     * 检查文件是否存在
     *
     * @param dirPath 目录路径
     * @param fileName 文件名
     * @return 如果文件存在返回 true，否则返回 false
     */
    public boolean checkFileExists(String dirPath, String fileName) {
        try {
            // 切换到指定目录
            if (checkDirectoryExists(dirPath)) {
                // 获取该目录下的所有文件列表
                FTPFile[] files = ftpClient.listFiles();
                // 遍历文件列表，检查文件是否存在
                for (FTPFile file : files) {
                    if (file.getName().equals(fileName)) {
                        return true; // 找到文件，返回 true
                    }
                }
                return false; // 如果未找到该文件，返回 false
            } else {
                System.out.println("目录不存在: " + dirPath);
                return false;
            }
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 下载指定目录和文件名的文件
     *
     * @param dirPath 目录路径
     * @param fileName 文件名
     * @return 返回文件的 InputStream，如果文件不存在则返回 null
     */
    public InputStream downloadFile(String dirPath, String fileName) {
        try {
            // 切换到指定目录
            if (ftpClient.changeWorkingDirectory(dirPath)) {
                // 获取文件的 InputStream
                return ftpClient.retrieveFileStream(fileName);
            } else {
                System.out.println("目录不存在: " + dirPath);
                return null;
            }
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

}