package com.dml.cloudtestserver.util.tool;

import lombok.extern.slf4j.Slf4j;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

@Slf4j
public class RequestLoggingFilter implements Filter {

    @Override
    public void init(FilterConfig filterConfig) {
        // 初始化逻辑
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) request;

        // 记录请求信息
        String remoteAddr = httpRequest.getRemoteAddr();
        String requestURI = httpRequest.getRequestURI();
        String method = httpRequest.getMethod();

        log.info("请求监控 -- IP: {}, Path: {}, Method: {}", remoteAddr, requestURI, method);

        // 继续请求链
        chain.doFilter(request, response);
    }

    @Override
    public void destroy() {
        // 销毁逻辑
    }
}