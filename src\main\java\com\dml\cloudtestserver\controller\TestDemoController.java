package com.dml.cloudtestserver.controller;


import com.dml.cloudtestserver.util.properties.CustomProperties;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;

@Tag(name = "测试（仅开发人员测试）")
@RestController
@RequestMapping("/test")
public class TestDemoController {

    @Autowired
    private CustomProperties customProperties;

    @Operation(summary = "下载日志文件")
    @GetMapping("/downloadLog/{fileIndex}")
    public ResponseEntity<InputStreamResource> downloadLog(@Parameter(description = "文件序号") @PathVariable String fileIndex
    ) {
        // 文件路径
        String repo_path = "/mnt/logdata/repo1";
        String filePath = repo_path + "/file_" + fileIndex + ".txt";

        // 检查文件是否存在
        File file = new File(filePath);
        if (!file.exists()) {
            throw new RuntimeException("文件不存在：" + filePath);
        }

        // 设置响应头
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + file.getName());

        // 返回文件流
        try {
            return ResponseEntity.ok()
                    .headers(headers)
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .contentLength(file.length())
                    .body(new InputStreamResource(Files.newInputStream(file.toPath())));
        } catch (IOException e) {
            throw new RuntimeException("读取文件失败：" + e.getMessage(), e);
        }
    }
}
