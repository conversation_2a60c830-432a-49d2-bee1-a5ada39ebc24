//package com.dml.cloudtestserver.controller;
//
//
//import cn.dev33.satoken.annotation.SaCheckPermission;
//import com.dml.cloudtestserver.model.role_power.RolePowerReq;
//import com.dml.cloudtestserver.service.RolePowerService;
//import com.dml.cloudtestserver.util.constants.Constants;
//import com.dml.cloudtestserver.util.response.ResponseResult;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.media.Schema;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.*;
//
//import javax.validation.Valid;
//
//@Tag(name = "角色权力管理")
//@RestController
//@RequestMapping("/api/rolePower")
//public class RolePowerController {
//
//    @Autowired
//    private RolePowerService rolePowerService;
//
//
//    @Operation(summary = "添加角色权力")
//    @PostMapping("")
//    @SaCheckPermission("role-power-add")
//    public ResponseResult<?> addRolePower(
//            @Schema(implementation = RolePowerReq.class) @Valid @RequestBody RolePowerReq rolePowerReq) {
//        return rolePowerService.addRolePower(rolePowerReq);
//    }
//
//
//    @Operation(summary = "删除角色权力")
//    @DeleteMapping("/{uid}")
//    @SaCheckPermission("role-power-delete")
//    public ResponseResult<?> deleteUser(@PathVariable Long uid) {
//        return rolePowerService.deleteRolePower(uid);
//    }
//
//
//    @Operation(summary = "批量查询角色权力")
//    @GetMapping("")
//    @SaCheckPermission("role-power-query")
//    public ResponseResult<?> queryAllRolePower(
//            @Parameter(description = "页码") @RequestParam(value = "pageNum", defaultValue = Constants.DEFAULT_PAGE_NUM) Integer pageNum,
//            @Parameter(description = "每页数量") @RequestParam(value = "pageSize", defaultValue = Constants.DEFAULT_PAGE_SIZE) Integer pageSize,
//            @Parameter(description = "权力范围") @RequestParam(value = "scope", required = false) Integer scope) {
//        return rolePowerService.queryAllRolePower(pageNum, pageSize, scope);
//    }
//
//
//}
