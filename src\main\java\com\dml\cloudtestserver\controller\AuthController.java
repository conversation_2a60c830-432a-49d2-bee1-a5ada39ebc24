package com.dml.cloudtestserver.controller;

import com.dml.cloudtestserver.model.auth.LoginReq;
import com.dml.cloudtestserver.service.AuthService;
import com.dml.cloudtestserver.util.response.ResponseResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;


@Tag(name = "验证")
@RestController
@RequestMapping("/api/auth")
public class AuthController {

    @Autowired
    private AuthService authService;


    @Operation(summary = "登录")
    @PostMapping("/login")
    public ResponseResult<?> login(@Valid @RequestBody LoginReq loginReq) {
        return authService.login(loginReq);
    }


    @Operation(summary = "退出登录")
    @PostMapping("logout")
    public ResponseResult<?> logout() {
        return authService.logout();
    }


    @Operation(summary = "获取登录用户的功能列表")
    @GetMapping("funcList")
    public ResponseResult<?> getFuncList() {
        return authService.getFuncList();
    }


    @Operation(summary = "获取登录用户的权限列表")
    @GetMapping("permissionList")
    public ResponseResult<?> getPermissionList() {
        return authService.getPermissionList();
    }


    @Operation(summary = "获取登录用户的批次列表")
    @GetMapping("batchList")
    public ResponseResult<?> getBatchList() {
        return authService.getBatchList();
    }


    @Operation(summary = "获取FTP信息")
    @GetMapping("ftpInfo/{key}")
    public ResponseResult<?> getFtpInfo(@PathVariable String key) {
        return authService.getFtpInfo(key);
    }

}
