package com.dml.cloudtestserver.controller;


import com.dml.cloudtestserver.service.EnumService;
import com.dml.cloudtestserver.util.response.ResponseResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "枚举信息")
@RestController
@RequestMapping("/api/enum")
public class EnumController {

    @Autowired
    private EnumService enumService;

    @Operation(summary = "查询枚举")
    @GetMapping("/{enumName}")
    public ResponseResult<?> queryEnum(@PathVariable String enumName) {
        System.out.println("enumName = " + enumName);
        return enumService.queryEnum(enumName);
    }


//    @Operation(summary = "查询所有枚举信息")
//    @GetMapping("all")
//    public ResponseResult<?> queryAllEnum() {
//        return enumService.queryAllEnum();
//    }
}
