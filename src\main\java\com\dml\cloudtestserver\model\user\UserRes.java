package com.dml.cloudtestserver.model.user;

import java.io.Serializable;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserRes implements Serializable {

    private static final long serialVersionUID = 6498999460874745592L;

    /**
     * UID
     */
    private Long uid;

    /**
     * 创建者UID
     */
    private Long createUserUid;

    /**
     * 创建者账户
     */
    private String createUserAccount;

    /**
     * 账号
     */
    private String account;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 类别
     */
    private Integer category;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 更新时间
     */
    private Date updatedTime;
}
