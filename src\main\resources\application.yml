spring:
  # 环境配置
  profiles:
    active: test

  # 资源不存在异常处理器开启
  mvc:
    throw-exception-if-no-handler-found: true
  web:
    resources:
      add-mappings: false

  # 日期格式化
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8

  # 文件上传设置
  servlet:
    multipart:
      max-file-size: 2MB
      max-request-size: 10MB


# 服务器配置
server:
  port: 8080 # 监听端口
  address: 0.0.0.0 # 开放端口
  max-http-header-size: 16384

# Sa-Token 配置
sa-token:
  token-name: token # token名称 (同时也是cookie名称)
  # token-prefix: Bearer  # token前缀
  timeout: -1 # token有效期，单位s 默认30天, -1代表永不过期
  activity-timeout: -1 # token临时有效期 (指定时间内无操作就视为token过期) 单位: 秒
  is-concurrent: true # 是否允许同一账号并发登录
  is-share: true # 在多人登录同一账号时，是否共用一个token
  token-style: uuid # token风格
  is-log: false # 是否输出操作日志
  jwt-secret-key: dml-jwt-secret-key # jwt秘钥


# SpringDoc API 文档配置
springdoc:
  # 分组配置
  group-configs:
    - group: All
      packagesToScan: com.dml.cloudtestserver.controller
      pathsToMatch: [ /api/** ]


# mybatis配置
mybatis:
  mapper-locations: classpath:mapper/**/*.xml  # xml存放路径（含mapper子目录）


# 分页插件配置
pagehelper:
  helper-dialect: mysql  # 指定数据库方言，PageHelper 会根据方言生成相应的分页 SQL。
  reasonable: true       # 合理化参数。如果为 true，当 pageNum < 1 时，自动将 pageNum 设置为 1；当 pageNum > 总页数时，自动将 pageNum 设置为总页数。
  support-methods-arguments: true  # 支持通过 Mapper 方法的参数来传递分页参数。启用后可以在 Mapper 方法的参数中动态传递 pageNum 和 pageSize。
  params: count=countSql  # 指定用于计算总记录数的参数名称。这里设置为 count=countSql，表示使用 countSql 参数来执行总记录数的查询操作。