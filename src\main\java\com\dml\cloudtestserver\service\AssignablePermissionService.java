package com.dml.cloudtestserver.service;

import cn.hutool.core.util.IdUtil;
import com.dml.cloudtestserver.dao.AssignablePermissionDao;
import com.dml.cloudtestserver.dao.PermissionDao;
import com.dml.cloudtestserver.model.assignable_permission.AssignablePermissionEntity;
import com.dml.cloudtestserver.model.assignable_permission.AssignablePermissionReq;
import com.dml.cloudtestserver.model.assignable_permission.AssignablePermissionRes;
import com.dml.cloudtestserver.model.permission.PermissionEntity;
import com.dml.cloudtestserver.util.response.ResponseEnum;
import com.dml.cloudtestserver.util.response.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AssignablePermissionService {

    @Autowired
    private AssignablePermissionDao assignablePermissionDao;

    @Autowired
    private PermissionDao permissionDao;

    /**
     * 批量更新可分配权限
     */
    public ResponseResult<?> updateAssignablePermission(AssignablePermissionReq assignablePermissionReq) {
        Integer userCategory = assignablePermissionReq.getUserCategory();
        List<Long> permissionUidList = assignablePermissionReq.getPermissionUidList();

        // 删除分类下的所有权限
        assignablePermissionDao.deleteAssignablePermissionList(userCategory);

        // 添加失败列表
        List<Long> errorPermissionUidList = new ArrayList<>();

        // 遍历添加
        for (Long permissionUid : permissionUidList) {
            // 数据对象
            AssignablePermissionEntity assignablePermissionEntity = new AssignablePermissionEntity(
                    IdUtil.getSnowflakeNextId(), userCategory, permissionUid);

            // 插入
            int affectedRows = assignablePermissionDao.insertAssignablePermission(assignablePermissionEntity);
            if (affectedRows == 0) {
                errorPermissionUidList.add(permissionUid);
            }
        }

        // 返回结果
        if (errorPermissionUidList.isEmpty()) {
            List<AssignablePermissionEntity> assignablePermissionEntityList = assignablePermissionDao
                    .queryAllAssignablePermission(userCategory);

            return ResponseResult.success(ResponseEnum.UPDATE_SUCCESS, assignablePermissionEntityList);
        } else {
            String errMsg = "更新失败: " + errorPermissionUidList.stream()
                    .map(String::valueOf) // 将 Long 转换为 String
                    .collect(Collectors.joining(", ")); // 使用空格连接

            return ResponseResult.error(ResponseEnum.UPDATE_ERROR, errMsg);
        }
    }

    /**
     * 批量查询可分配权限
     */
    public ResponseResult<?> queryAllAssignablePermission(Integer userCategory) {
        List<AssignablePermissionEntity> assignablePermissionEntityList = assignablePermissionDao
                .queryAllAssignablePermission(userCategory);

        List<AssignablePermissionRes> assignablePermissionResList = new ArrayList<>();

        for (AssignablePermissionEntity assignablePermissionEntity : assignablePermissionEntityList) {
            Long permissionUid = assignablePermissionEntity.getPermissionUid();
            PermissionEntity permissionEntity = permissionDao.selectPermission(permissionUid);

            if (permissionEntity != null) {
                AssignablePermissionRes assignablePermissionRes = new AssignablePermissionRes(
                        assignablePermissionEntity.getUid(),
                        assignablePermissionEntity.getUserCategory(),
                        assignablePermissionEntity.getPermissionUid(),
                        permissionEntity.getScope(),
                        permissionEntity.getName(),
                        permissionEntity.getParentUid(),
                        permissionEntity.getCode(),
                        permissionEntity.getSortOrder()
                );
                assignablePermissionResList.add(assignablePermissionRes);
            }
        }
        return ResponseResult.success(ResponseEnum.QUERY_SUCCESS, assignablePermissionResList);
    }

}
