<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dml.cloudtestserver.dao.AssignablePermissionDao">


    <resultMap id="BaseResultMap" type="com.dml.cloudtestserver.model.assignable_permission.AssignablePermissionEntity">
        <result column="uid" property="uid"/>
        <result column="user_category" property="userCategory"/>
        <result column="permission_uid" property="permissionUid"/>
    </resultMap>


    <insert id="insertAssignablePermission"
            parameterType="com.dml.cloudtestserver.model.assignable_permission.AssignablePermissionEntity">
        INSERT INTO tb_assignable_permission (uid, user_category, permission_uid)
        VALUES (#{uid}, #{userCategory}, #{permissionUid})
    </insert>

    <delete id="deleteAssignablePermissionList" parameterType="java.lang.Integer">
        DELETE
        FROM tb_assignable_permission
        WHERE user_category = #{userCategory}
    </delete>

    <select id="queryAllAssignablePermission" resultMap="BaseResultMap">
        SELECT uid, user_category, permission_uid
        FROM tb_assignable_permission
        <where>
            <if test="userCategory != null">
                AND user_category = #{userCategory}
            </if>
        </where>
    </select>

</mapper>