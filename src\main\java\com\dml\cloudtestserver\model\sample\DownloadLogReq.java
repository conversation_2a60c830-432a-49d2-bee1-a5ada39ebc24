package com.dml.cloudtestserver.model.sample;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.io.Serializable;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class DownloadLogReq implements Serializable {

    private static final long serialVersionUID = 1210600049510272892L;

    /**
     * 样片号（flash_uid）
     */
    @Schema(description = "样片号（flash_uid）。必须为 32 位数字和字母(大写)组合")
    @NotBlank(message = "样片号（flash_uid）不能为空")
    @Pattern(regexp = "^[A-Z0-9]{32}$", message = "样片号（flash_uid）必须为 32 位数字和字母(大写)组合")
    private String sampleId;

}
