package com.dml.cloudtestserver.util.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "custom-config")
public class CustomProperties {

    /**
     * 服务器IP
     */
    private String serverIp;

    /**
     * MP日志文件存放仓库
     */
    private String mpLogRepo;

    /**
     * MP日志文件后缀
     */
    private String mpLogSuffix;

    /**
     * websocket更新频率（秒）
     */
    private Integer websocketUpdateFrequency;
}