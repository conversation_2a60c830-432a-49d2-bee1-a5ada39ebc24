package com.dml.cloudtestserver.model.batch;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class BatchInfoRes implements Serializable {

    private static final long serialVersionUID = 4778112928795333205L;

    /**
     * UID
     */
    private Long uid;

    /**
     * 批次号
     */
    private String batchId;

    /**
     * 预生产样片数量
     */
    private Integer preProductionSampleNum;

    /**
     * 批次状态：1-未开始，2-运行中，4-已停止
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createdTime;
}