<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dml.cloudtestserver.dao.UserPowerDao">


    <!-- 结果映射，将数据库字段映射到 UserPowerEntity 对象的属性 -->
    <resultMap id="BaseResultMap" type="com.dml.cloudtestserver.model.user_power.UserPowerEntity">
        <result column="uid" property="uid"/>
        <result column="user_uid" property="userUid"/>
        <result column="scope" property="scope"/>
        <result column="power_uid" property="powerUid"/>
        <result column="granted_time" property="grantedTime"/>
    </resultMap>


    <delete id="deleteUserPowerList" parameterType="Long">
        DELETE
        FROM tb_user_power
        WHERE user_uid = #{userUid}
    </delete>


    <delete id="deleteUserPowerListByBatchUid" parameterType="Long">
        DELETE
        FROM tb_user_power
        WHERE scope = 2
          AND power_uid = #{batchUid}
    </delete>

    <select id="selectUserAllPower" resultMap="BaseResultMap">
        SELECT uid, user_uid, scope, power_uid, granted_time
        FROM tb_user_power
        WHERE user_uid = #{userUid}
        <if test="scope != null">
            AND scope = #{scope}
        </if>
    </select>


    <insert id="insertUserPower" parameterType="com.dml.cloudtestserver.model.user_power.UserPowerDto">
        INSERT INTO tb_user_power (uid, user_uid, scope, power_uid)
        VALUES (#{uid}, #{userUid}, #{scope}, #{powerUid})
    </insert>


    <select id="existsUidAndPowerId" resultType="boolean">
        SELECT EXISTS (SELECT 1
                       FROM tb_user_power
                       WHERE user_uid = #{userUid}
                         AND power_uid = #{powerUid})
    </select>


    <select id="selectAllPermissionUid" resultType="Long">
        SELECT power_uid
        FROM tb_user_power
        WHERE user_uid = #{userUid}
          AND scope = 1
    </select>


    <select id="selectAllBatchUid" resultType="Long">
        SELECT power_uid
        FROM tb_user_power
        WHERE user_uid = #{userUid}
          AND scope = 2
    </select>


</mapper>
