package com.dml.cloudtestserver.service;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.IdUtil;
import com.dml.cloudtestserver.dao.*;
import com.dml.cloudtestserver.model.batch.BatchRes;
import com.dml.cloudtestserver.model.sample.*;
import com.dml.cloudtestserver.model.user.UserEntity;
import com.dml.cloudtestserver.util.constants.BatchStatusEnum;
import com.dml.cloudtestserver.util.constants.Constants;
import com.dml.cloudtestserver.util.constants.UploadStatusEnum;
import com.dml.cloudtestserver.util.constants.UserCategoryEnum;
import com.dml.cloudtestserver.util.properties.CustomProperties;
import com.dml.cloudtestserver.util.response.ResponseEnum;
import com.dml.cloudtestserver.util.response.ResponseResult;
import com.dml.cloudtestserver.util.tool.FtpTool;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


@Slf4j
@Service
public class SampleService {

    @Autowired
    private SampleDao sampleDao;

    @Autowired
    private BatchDao batchDao;

    @Autowired
    private TableDao tableDao;

    @Autowired
    private UserDao userDao;

    @Autowired
    private UserPowerDao userPowerDao;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private CustomProperties customProperties;

    @Autowired
    private FtpTool ftpTool;


    /**
     * 根据批次唯一标识获取批次ID和其他相关数据
     *
     * @param batchUid 批次的唯一标识
     * @return 返回包含批次ID和其他相关数据的Map
     */
    private Map<String, String> getBatchIdAndTbSampleName(Long batchUid) {
        // 通过批次唯一标识从数据库中查询批次实体
        BatchRes batchRes = batchDao.selectBatch(batchUid);
        if (batchRes == null) {
            throw new IllegalArgumentException("Batch entity not found for UID: " + batchUid);
        }

        // 创建一个用于存储批次相关数据的Map
        Map<String, String> data = new HashMap<>();

        // 获取批次ID
        String batchId = batchRes.getBatchId();

        // 将批次ID放入数据Map中
        data.put("batchId", batchId);

        // 构造并命名样本名称，并将其放入数据Map中
        data.put("tbSampleName", Constants.TB_SAMPLE_NAME_PREFIX + batchRes.getUid());

        return data;
    }


    /**
     * 检测批次号、样片表是否存在
     *
     * @param batchUid 批次UID
     * @return 响应结果
     */
    private ResponseResult<?> checkBatch(Long batchUid) {
        if (!batchDao.existsUid(batchUid)) {
            return ResponseResult.error(ResponseEnum.DATA_NOT_EXISTS, "批次不存在");
        }

        String tbSampleName = getBatchIdAndTbSampleName(batchUid).get("tbSampleName");

        if (tableDao.existTable(tbSampleName) == 0) {
            return ResponseResult.error(ResponseEnum.DATA_NOT_EXISTS, "样片表不存在");
        }

        return null;
    }


    /**
     * 获取日志目录
     *
     * @param sampleId 样片ID
     * @return 日志目录
     */
    private String getLogDir(String sampleId) {
        String hynixCode = sampleId.substring(0, 2);
        String fabCode = sampleId.substring(2, 4);
        String runNo = sampleId.substring(4, 6);
        String year = sampleId.substring(20, 22);
        String month = sampleId.substring(18, 20);
        String date = sampleId.substring(16, 18);
        String hour = sampleId.substring(12, 14);
        String warfeNo = sampleId.substring(6, 8);

        return customProperties.getMpLogRepo() + "/" + hynixCode + "/" + fabCode + "/" + runNo + "/" + year + "/" + month + "/" + date + "/" + hour + "/" + warfeNo + "/" + sampleId;
    }


    /**
     * 获取时间戳字符串
     */
    public static String getTimestamp() {
        String timeZone = "Asia/Shanghai";  // 输入时区
        String format = "yyyy-MM-dd_HH-mm-ss-SSS";  // 输入格式

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format).withZone(ZoneId.of(timeZone));
        return ZonedDateTime.now(ZoneId.of(timeZone)).format(formatter);
    }


    /**
     * 获取最新的日志文件名
     *
     * @param sampleId 样片ID
     * @param logDir   日志目录
     * @return 最新的日志文件名
     */
    private String getLatestLogFileName(String sampleId, String logDir) {
        // 匹配形如 sampleId + _2023-06-15_14-30-55 的文件名
        Pattern datePattern = Pattern.compile(
                sampleId + "_(\\d{4}-\\d{2}-\\d{2})_(\\d{2}-\\d{2}-\\d{2}-\\d{3})");

        FTPClient ftpClient = new FTPClient();

        try {
            // 连接到 FTP 服务器
            if (!ftpTool.connect()) {
                throw new RuntimeException("连接到 FTP 服务器失败");
            }

            // 更改工作目录
            ftpClient.changeWorkingDirectory(logDir);

            // 列出目录中的文件
            FTPFile[] files = ftpClient.listFiles();

            // 筛选匹配的文件名
            List<String> matchingFileNames = Arrays.stream(files)
                    .map(FTPFile::getName)
                    .filter(fileName -> datePattern.matcher(fileName).find())
                    .collect(Collectors.toList());

            if (matchingFileNames.isEmpty()) {
                // 没有匹配的文件
                return null;
            } else if (matchingFileNames.size() == 1) {
                // 只有一个匹配的文件
                return matchingFileNames.get(0);
            } else {
                // 多个匹配的文件，找到最新的
                Optional<String> latestFileName = matchingFileNames.stream()
                        .max(Comparator.comparing(fileName -> {
                            Matcher matcher = datePattern.matcher(fileName);
                            if (matcher.find()) {
                                String datePart = matcher.group(1) + " " + matcher.group(2).replace("-", ":");
                                return LocalDateTime.parse(datePart, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss-SSS"));
                            }
                            // 不应达到这里，因为已经过滤过一次
                            return null;
                        }));

                return latestFileName.orElse(null);
            }
        } catch (IOException e) {
            throw new RuntimeException("读取日志目录失败：" + e.getMessage(), e);
        } finally {
            ftpTool.disconnect(); // 确保断开连接
        }
    }


//    /**
//     * 获取最新的日志文件名
//     *
//     * @param logDir 日志目录
//     * @return 最新的日志文件名
//     */
//    private String getLatestLogFileName(String sampleId, String logDir) {
//        // 匹配形如 sampleId + _2023-06-15_14-30-55 的文件名
//        Pattern datePattern = Pattern.compile(
//                sampleId + "_(\\d{4}-\\d{2}-\\d{2})_(\\d{2}-\\d{2}-\\d{2})");
//        Path dirPath = Paths.get(logDir);
//
//        try (Stream<Path> paths = Files.list(dirPath)) {
//            List<String> matchingFileNames = paths
//                    .map(Path::getFileName)
//                    .map(Path::toString)
//                    .filter(fileName -> datePattern.matcher(fileName).find())
//                    .collect(Collectors.toList());
//
//            if (matchingFileNames.isEmpty()) {
//                // 没有匹配的文件
//                return null;
//            } else if (matchingFileNames.size() == 1) {
//                // 只有一个匹配的文件
//                return matchingFileNames.get(0);
//            } else {
//                // 多个匹配的文件，找到最新的
//                return matchingFileNames.stream()
//                        .max(Comparator.comparing(fileName -> {
//                            Matcher matcher = datePattern.matcher(fileName);
//                            if (matcher.find()) {
//                                String datePart = matcher.group(1) + " " + matcher.group(2).replace("-", ":");
//                                return LocalDateTime.parse(datePart, DateTimeFormatter.ofPattern("yyyy-MM-dd_HH-mm-ss-SSS"));
//                            }
//                            // 不应达到这里，因为已经过滤过一次
//                            return null;
//                        }))
//                        .orElse("");
//            }
//        } catch (IOException e) {
//            throw new RuntimeException("读取日志目录失败：" + e.getMessage(), e);
//        }
//    }


    /**
     * 更新Redis当前批次列表
     */
    private void updateRedisTodayBatchUidList(Long batchUid) {
        // 检查键是否存在
        if (Boolean.FALSE.equals(stringRedisTemplate.hasKey(Constants.REDIS_KEY_TODAY_BATCH_UID_LIST))) {
            // 如果键不存在，添加元素
            stringRedisTemplate.opsForSet().add(Constants.REDIS_KEY_TODAY_BATCH_UID_LIST, String.valueOf(batchUid));

            // 设置有效期到每天的 23:59:59
            LocalDateTime endOfDay = LocalDate.now().atTime(23, 59, 59);
            Date expirationDate = Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
            stringRedisTemplate.expireAt(Constants.REDIS_KEY_TODAY_BATCH_UID_LIST, expirationDate);
        } else {
            // 如果键不存在，添加元素
            stringRedisTemplate.opsForSet().add(Constants.REDIS_KEY_TODAY_BATCH_UID_LIST, String.valueOf(batchUid));
        }

        // 高并发场景下，使用Lua脚本进行原子操作
//        String luaScript =
//                "local exists = redis.call('EXISTS', KEYS[1]) " +
//                        "redis.call('SADD', KEYS[1], ARGV[1]) " +
//                        "if exists == 0 then " +
//                        "   local expiration = ARGV[2] " +
//                        "   redis.call('EXPIREAT', KEYS[1], expiration) " +
//                        "end";
//
//        // 计算每天的 23:59:59
//        LocalDateTime endOfDay = LocalDate.now().atTime(23, 59, 59);
//        long expirationTimestamp = endOfDay.atZone(ZoneId.systemDefault()).toInstant().getEpochSecond();
//
//        // 执行 Lua 脚本
//        stringRedisTemplate.execute(
//                new DefaultRedisScript<>(luaScript, Long.class),
//                Collections.singletonList(Constants.REDIS_KEY_TODAY_BATCH_UID_LIST),
//                batchUid,
//                expirationTimestamp
//        );
    }


    /**
     * 添加样片
     */
    @Transactional
    public ResponseResult<?> addSample(Long batchUid, SampleInsertReq sampleInsertReq) {
        // 校验批次号和样片表是否存在
        ResponseResult<?> checkBatchResult = checkBatch(batchUid);
        if (checkBatchResult != null) {
            return checkBatchResult;
        }

        // 获取样片表名
        Map<String, String> map = getBatchIdAndTbSampleName(batchUid);
        String tbSampleName = map.get("tbSampleName");
        String batchId = map.get("batchId");

        // 判断批次是否运行中
        BatchRes batchRes = batchDao.selectBatch(batchUid);
        if (batchRes.getStatus().equals(BatchStatusEnum.UNSTARTED.getValue())) {
            return ResponseResult.error(ResponseEnum.ADD_ERROR, "添加失败，批次未开始");
        } else if (batchRes.getStatus().equals(BatchStatusEnum.STOPPED.getValue())) {
            return ResponseResult.error(ResponseEnum.ADD_ERROR, "添加失败，批次已停止");
        }

        // 判断用户是否有该批次权限
        Long loginUserUid = StpUtil.getLoginIdAsLong();
        UserEntity loginUserEntity = userDao.selectUser(loginUserUid);

        if (!loginUserEntity.getCategory().equals(UserCategoryEnum.SUPER_ADMIN.getValue())) {
            if (!userPowerDao.existsUidAndPowerId(loginUserUid, batchUid)) {
                return ResponseResult.error(ResponseEnum.ADD_ERROR, "添加失败，用户没有该批次权限");
            }
        }

        // 样片ID
        String sampleId = sampleInsertReq.getSampleId();

        // 生成唯一记录ID
        Long uid = IdUtil.getSnowflakeNextId();

        // 日志目录
        String logDir = getLogDir(sampleId);

        // 日志名称
        String timeStamp = getTimestamp();
        String logName = sampleId + "_" + timeStamp + customProperties.getMpLogSuffix();

        // 日志上传状态初始化为未完成
        Integer logUploadStatus = UploadStatusEnum.UNSTARTED.getValue();

        // 创建样片插入数据传输对象
        SampleInsertDto sampleInsertDto = new SampleInsertDto(
                uid,
                batchUid,
                sampleId,
                sampleInsertReq.getFlashType(),
                sampleInsertReq.getFlashNum(),
                sampleInsertReq.getProductType(),
                sampleInsertReq.getControllerType(),
                sampleInsertReq.getInterfaceType(),
                sampleInsertReq.getCapacity(),
                sampleInsertReq.getCapacityMode(),
                sampleInsertReq.getSpeedType(),
                sampleInsertReq.getProductionMode(),
                sampleInsertReq.getProductionVersion(),
                sampleInsertReq.getProductionResult(),
                sampleInsertReq.getErrorCode(),
                sampleInsertReq.getOsPlatform(),
                logDir,
                logName,
                logUploadStatus
        );

        // 插入样片量产信息数据到MySQL
        int affectedRows = sampleDao.insertSample(tbSampleName, sampleInsertDto);
        if (affectedRows == 0) {
            // 插入失败，返回错误结果
            return ResponseResult.error(ResponseEnum.ADD_ERROR);
        }

        // 更新当前样片的批次号到redis
        try {
            updateRedisTodayBatchUidList(batchUid);
        } catch (Exception e) {
            log.error("更新当前样片的批次号到redis: {}", e.getMessage(), e);
            return ResponseResult.error(ResponseEnum.INTERNAL_SERVER_ERROR);
        }

        // 查询数据
        SampleEntity sampleEntity = sampleDao.selectSample(tbSampleName, uid);

        // 组装数据
        SampleRes sampleRes = new SampleRes(
                sampleEntity.getUid(),
                sampleEntity.getBatchUid(),
                batchId,
                sampleEntity.getSampleId(),
                sampleEntity.getFlashType(),
                sampleEntity.getFlashNum(),
                sampleEntity.getProductType(),
                sampleEntity.getControllerType(),
                sampleEntity.getInterfaceType(),
                sampleEntity.getCapacity(),
                sampleEntity.getCapacityMode(),
                sampleEntity.getSpeedType(),
                sampleEntity.getProductionMode(),
                sampleEntity.getProductionVersion(),
                sampleEntity.getProductionResult(),
                sampleEntity.getErrorCode(),
                sampleEntity.getOsPlatform(),
                logDir,
                logName,
                logUploadStatus,
                sampleEntity.getCreatedTime()
        );

        // 插入成功，返回成功结果和样片数据
        return ResponseResult.success(ResponseEnum.ADD_SUCCESS, sampleRes);
    }


    /**
     * 更改样片
     */
    @Transactional
    public ResponseResult<?> updateSample(Long batchUid, Long uid, SampleUpdateReq sampleUpdateReq) {
        // 校验批次号和样片表是否存在
        ResponseResult<?> checkBatchResult = checkBatch(batchUid);
        if (checkBatchResult != null) {
            return checkBatchResult;
        }

        // 获取样片表名
        String tbSampleName = getBatchIdAndTbSampleName(batchUid).get("tbSampleName");

        // 查询数据
        SampleEntity sampleEntity = sampleDao.selectSample(tbSampleName, uid);
        System.out.println("sampleEntity: " + sampleEntity);

        // 旧数据
        Integer oldLogUploadStatus = sampleEntity.getLogUploadStatus();

        // 新数据
        Integer newLogUploadStatus = sampleUpdateReq.getLogUploadStatus();

        // 判断是否数据未改变
        if (newLogUploadStatus == null || oldLogUploadStatus.equals(newLogUploadStatus)) {
            return ResponseResult.error(ResponseEnum.DATA_NOT_CHANGE);
        }

        // 更新数据
        SampleUpdateDto sampleUpdateDto = new SampleUpdateDto(uid, newLogUploadStatus);
        int affectedRows = sampleDao.updateSample(tbSampleName, sampleUpdateDto);
        if (affectedRows == 0) {
            return ResponseResult.error(ResponseEnum.UPDATE_ERROR);
        }

        // 更新成功，返回成功结果和样片数据
        return ResponseResult.success(ResponseEnum.UPDATE_SUCCESS);
    }


    /**
     * 查询单个样片
     */
    public ResponseResult<?> querySample(Long batchUid, Long uid) {
        // 校验批次号和样片表是否存在
        ResponseResult<?> checkBatchResult = checkBatch(batchUid);
        if (checkBatchResult != null) {
            return checkBatchResult;
        }

        // 样片表名称
        Map<String, String> map = getBatchIdAndTbSampleName(batchUid);
        String tbSampleName = map.get("tbSampleName");
        String batchId = map.get("batchId");

        // 查询数据
        SampleEntity sampleEntity = sampleDao.selectSample(tbSampleName, uid);
        if (sampleEntity == null) {
            return ResponseResult.error(ResponseEnum.DATA_NOT_EXISTS, "样片量产信息不存在");
        }

        // 组装数据
        SampleRes sampleRes = new SampleRes(
                sampleEntity.getUid(),
                sampleEntity.getBatchUid(),
                batchId,
                sampleEntity.getSampleId(),
                sampleEntity.getFlashType(),
                sampleEntity.getFlashNum(),
                sampleEntity.getProductType(),
                sampleEntity.getControllerType(),
                sampleEntity.getInterfaceType(),
                sampleEntity.getCapacity(),
                sampleEntity.getCapacityMode(),
                sampleEntity.getSpeedType(),
                sampleEntity.getProductionMode(),
                sampleEntity.getProductionVersion(),
                sampleEntity.getProductionResult(),
                sampleEntity.getErrorCode(),
                sampleEntity.getOsPlatform(),
                sampleEntity.getLogDir(),
                sampleEntity.getLogName(),
                sampleEntity.getLogUploadStatus(),
                sampleEntity.getCreatedTime()
        );

        // 查询成功，返回成功结果和样片数据
        return ResponseResult.success(ResponseEnum.QUERY_SUCCESS, sampleRes);
    }


    /**
     * 批量查询样片
     */
    public ResponseResult<?> querySampleList(
            int pageNum,
            int pageSize,
            Long batchUid,
            String sampleId,
            List<Integer> productType,
            List<Integer> productionResult,
            List<Integer> logUploadStatus,
            Date startCreatedTime,
            Date endCreatedTime) {

        // 组装SampleRes对象列表
        List<SampleRes> sampleResList = new ArrayList<>();

        // 校验批次号和样片表是否存在
        if (!batchDao.existsUid(batchUid)) {
            return ResponseResult.success(ResponseEnum.QUERY_SUCCESS, sampleResList);
        }

        String tbSampleName = getBatchIdAndTbSampleName(batchUid).get("tbSampleName");

        if (tableDao.existTable(tbSampleName) == 0) {
            return ResponseResult.error(ResponseEnum.DATA_NOT_EXISTS, "样片表不存在");
        }

        // 样片表名称
        Map<String, String> map = getBatchIdAndTbSampleName(batchUid);
        String batchId = map.get("batchId");

        // 开启分页
        PageHelper.startPage(pageNum, pageSize);

        // 查询数据
        List<SampleEntity> sampleEntityList = sampleDao.selectSampleList(
                tbSampleName, sampleId, productType, productionResult, logUploadStatus, startCreatedTime, endCreatedTime);

        for (SampleEntity sampleEntity : sampleEntityList) {
            // 组装SampleRes对象
            SampleRes sampleRes = new SampleRes(
                    sampleEntity.getUid(),
                    sampleEntity.getBatchUid(),
                    batchId,
                    sampleEntity.getSampleId(),
                    sampleEntity.getFlashType(),
                    sampleEntity.getFlashNum(),
                    sampleEntity.getProductType(),
                    sampleEntity.getControllerType(),
                    sampleEntity.getInterfaceType(),
                    sampleEntity.getCapacity(),
                    sampleEntity.getCapacityMode(),
                    sampleEntity.getSpeedType(),
                    sampleEntity.getProductionMode(),
                    sampleEntity.getProductionVersion(),
                    sampleEntity.getProductionResult(),
                    sampleEntity.getErrorCode(),
                    sampleEntity.getOsPlatform(),
                    sampleEntity.getLogDir(),
                    sampleEntity.getLogName(),
                    sampleEntity.getLogUploadStatus(),
                    sampleEntity.getCreatedTime()
            );
            sampleResList.add(sampleRes);
        }

        // 创建PageInfo<SampleRes>对象
        PageInfo<SampleRes> pageInfo = new PageInfo<>(sampleResList);
        pageInfo.setTotal(((Page<SampleEntity>) sampleEntityList).getTotal()); // 获取总数并设置

        // 返回结果
        return ResponseResult.success(ResponseEnum.QUERY_SUCCESS, pageInfo);
    }


    /**
     * 查询批次下所有样片号
     */
    public ResponseResult<?> queryBatchAllSampleId(Long batchUid, int pageNum, int pageSize) {
        // 校验批次号和样片表是否存在
        ResponseResult<?> checkBatchResult = checkBatch(batchUid);
        if (checkBatchResult != null) {
            return checkBatchResult;
        }

        // 样片表名称
        String tbSampleName = getBatchIdAndTbSampleName(batchUid).get("tbSampleName");

        // 开启分页
        PageHelper.startPage(pageNum, pageSize);

        // 查询数据
        List<SampleIdRes> sampleIdResList = sampleDao.selectAllSampleId(tbSampleName);

        // 返回结果
        return ResponseResult.success(ResponseEnum.QUERY_SUCCESS, sampleIdResList);
    }


    /**
     * 下载最新的日志文件
     */
    public ResponseEntity<?> downloadLatestLog(String sampleId) {
        try {
            // 获取日志文件目录
            String logDir = getLogDir(sampleId);

            // 连接到 FTP
            if (!ftpTool.connect()) {
                return ResponseEntity.ok().body(ResponseResult.error(ResponseEnum.FTP_CONNECT_ERROR));
            }

            // 判断目录是否存在
            boolean exists = ftpTool.checkDirectoryExists(logDir);
            if (!exists) {
                return ResponseEntity.ok().body(ResponseResult.error(ResponseEnum.DATA_NOT_EXISTS, "日志文件不存在"));
            }

            // 找到所有符合正则格式的文件，并筛选出日期最新的文件名
            String logName = getLatestLogFileName(sampleId, logDir);
            if (logName != null && logName.isEmpty()) {
                return ResponseEntity.ok().body(ResponseResult.error(ResponseEnum.DATA_NOT_EXISTS, "日志文件不存在"));
            }

            // 获取文件输入流
            InputStream inputStream = ftpTool.downloadFile(logDir, logName);
            if (inputStream == null) {
                return ResponseEntity.ok().body(ResponseResult.error(ResponseEnum.DOWNLOAD_ERROR));
            }

            // 断开连接
            ftpTool.disconnect();

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + logName);

            // 返回文件流
            return ResponseEntity.ok()
                    .headers(headers)
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(new InputStreamResource(inputStream));

        } catch (Exception e) {
            return ResponseEntity.ok().body(ResponseResult.error(ResponseEnum.DOWNLOAD_ERROR));
        }

    }


    /**
     * 下载日志文件
     */
    public ResponseEntity<?> downloadLog(Long batchUid, Long sampleUid) {
        try {
            if (!batchDao.existsUid(batchUid)) {
                return ResponseEntity.ok().body(ResponseResult.error(ResponseEnum.DATA_NOT_EXISTS, "批次不存在"));
            }

            String tbSampleName = getBatchIdAndTbSampleName(batchUid).get("tbSampleName");

            if (tableDao.existTable(tbSampleName) == 0) {
                return ResponseEntity.ok().body(ResponseResult.error(ResponseEnum.DATA_NOT_EXISTS, "样片表不存在"));
            }

            SampleEntity sampleEntity = sampleDao.selectSample(tbSampleName, sampleUid);
            String logDir = sampleEntity.getLogDir();
            String logName = sampleEntity.getLogName();
            String absoluteLogDir = customProperties.getMpLogRepo() + logDir;

            // 连接到 FTP
            if (!ftpTool.connect()) {
                return ResponseEntity.ok().body(ResponseResult.error(ResponseEnum.FTP_CONNECT_ERROR));
            }

            // 判断目录是否存在
            boolean dirExists = ftpTool.checkDirectoryExists(logDir);
            if (!dirExists) {
                return ResponseEntity.ok().body(ResponseResult.error(ResponseEnum.DATA_NOT_EXISTS, "日志文件不存在"));
            }

            // 判断文件是否存在
            boolean fileExists = ftpTool.checkFileExists(absoluteLogDir, logName);
            if (!fileExists) {
                return ResponseEntity.ok().body(ResponseResult.error(ResponseEnum.DATA_NOT_EXISTS, "日志文件不存在"));
            }

            // 获取文件输入流
            InputStream inputStream = ftpTool.downloadFile(logDir, logName);
            if (inputStream == null) {
                return ResponseEntity.ok().body(ResponseResult.error(ResponseEnum.DOWNLOAD_ERROR));
            }

            // 断开连接
            ftpTool.disconnect();

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + logName);

            // 返回文件流
            return ResponseEntity.ok()
                    .headers(headers)
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(new InputStreamResource(inputStream));

        } catch (Exception e) {
            return ResponseEntity.ok().body(ResponseResult.error(ResponseEnum.DOWNLOAD_ERROR));
        }
    }


//    /**
//     * 创建分片存储路径。
//     *
//     * @param logTaskId 分片所属的任务ID
//     * @param chunkId   分片ID
//     * @return 存储路径
//     */
//    private String createChunkStorePath(String logTaskId, String chunkId) {
//        String chunkFolderPath = customConfig.getMpLogChunkPath() + File.separator + logTaskId;
//
//        // 创建目录
//        Path path = Paths.get(chunkFolderPath);
//        if (!Files.exists(path)) {
//            try {
//                Files.createDirectories(path);
//            } catch (IOException e) {
//                throw new RuntimeException("分片目录路径创建失败: " + chunkFolderPath, e);
//            }
//        }
//
//        String fileName = chunkId + Constants.TEMP_FILE_SUFFIX;
//        return chunkFolderPath + File.separator + fileName;
//    }


//    /**
//     * 上传分片文件到指定路径。
//     *
//     * @param logTaskId 分片所属的任务ID
//     * @param chunkId   分片ID
//     * @param chunkFile 分片文件
//     * @throws IOException 如果文件操作失败
//     */
//    public void uploadChunk(String logTaskId, String chunkId, MultipartFile chunkFile) throws IOException {
//        // 创建存储路径
//        String chunkStorePath = createChunkStorePath(logTaskId, chunkId);
//
//        // 创建目标文件
//        File targetFile = new File(chunkStorePath);
//
//        // 保存文件
//        try (FileOutputStream fos = new FileOutputStream(targetFile)) {
//            fos.write(chunkFile.getBytes());
//        } catch (IOException e) {
//            throw new RuntimeException("保存分片文件失败", e);
//        }
//    }


//    /**
//     * 合并分片
//     *
//     * @param logTaskId        日志任务ID
//     * @param logChunkInfoList 分片信息列表
//     * @param mergedFilePath   合并文件存放地址
//     */
//    private void mergeChunks(String logTaskId, List<LogChunkInfoDto> logChunkInfoList, String mergedFilePath) {
//        // 获取分片文件路径
//        String chunkFolderPath = customConfig.getMpLogChunkPath() + File.separator + logTaskId;
//
//        // 创建合并文件对象
//        File mergedFile = new File(mergedFilePath);
//
//        try (FileOutputStream fos = new FileOutputStream(mergedFile);
//             BufferedOutputStream mergingStream = new BufferedOutputStream(fos)) {
//
//            // 按照顺序读取每个分片的内容并写入合并文件
//            for (LogChunkInfoDto logChunkInfo : logChunkInfoList) {
//                String chunkId = logChunkInfo.getChunkId();
//                String chunkFilePath = chunkFolderPath + File.separator + chunkId + Constants.TEMP_FILE_SUFFIX;
//
//                File chunkFile = new File(chunkFilePath);
//                if (chunkFile.exists()) {
//                    // 读取分片文件内容
//                    try (FileInputStream fis = new FileInputStream(chunkFile);
//                         BufferedInputStream chunkStream = new BufferedInputStream(fis)) {
//
//                        byte[] buffer = new byte[1024];
//                        int bytesRead;
//                        while ((bytesRead = chunkStream.read(buffer)) != -1) {
//                            mergingStream.write(buffer, 0, bytesRead); // 写入合并文件
//                        }
//                    }
//                } else {
//                    throw new RuntimeException("分片文件不存在: " + chunkFilePath);
//                }
//            }
//            mergingStream.flush(); // 确保所有数据写入到文件中
//        } catch (IOException e) {
//            throw new RuntimeException("合并分片文件失败", e);
//        }
//    }


//    /**
//     * 删除分片文件所在目录及其内容
//     *
//     * @param logTaskId 日志任务ID
//     */
//    private void deleteChunks(String logTaskId) {
//        // 获取分片文件路径
//        String chunkFolderPath = customConfig.getMpLogChunkPath() + File.separator + logTaskId;
//
//        // 转换为Path对象
//        Path chunkFolder = Paths.get(chunkFolderPath);
//
//        // 检查目录是否存在
//        if (Files.exists(chunkFolder) && Files.isDirectory(chunkFolder)) {
//            try {
//                // 使用Files.walk()删除目录及其内容
//                Files.walk(chunkFolder)
//                        .sorted(Comparator.reverseOrder())
//                        .map(Path::toFile)
//                        .forEach(File::delete);
//            } catch (IOException e) {
//                throw new RuntimeException("删除分片文件所在目录失败: " + chunkFolderPath, e);
//            }
//        } else {
//            throw new RuntimeException("分片文件所在目录不存在: " + chunkFolderPath);
//        }
//    }

//    /**
//     * 获取可用的仓库
//     *
//     * @return 可用的仓库
//     */
//    public String getAvailableRepoPath() {
//        // 将警告值从GB转换为字节
//        long warningSizeInBytes = customConfig.getMpLogRepoWarningSize() * 1024L * 1024L * 1024L;
//
//        // 遍历mpLogRepoList中的每个路径
//        for (String repoPath : customConfig.getMpLogRepoList()) {
//            // 获取仓库目录
//            File repoDir = new File(repoPath);
//
//            // 检查目录是否存在
//            if (repoDir.exists() && repoDir.isDirectory()) {
//                // 获取可用空间大小
//                long availableSpace = repoDir.getFreeSpace();
//
//                // 检查可用空间是否大于等于警告值
//                if (availableSpace >= warningSizeInBytes) {
//                    // 返回可用的目录路径
//                    return repoPath;
//                }
//            }
//        }
//
//        // 如果没有找到合适的目录，则抛出异常或返回默认值
//        throw new RuntimeException("没有足够的空间用于合并文件");
//    }


//    /**
//     * 上传日志分片
//     */
//    public ResponseResult<?> uploadLog(
//            Long batchUid,
//            String logTaskId,
//            String chunkId,
//            MultipartFile chunkFile) {
//
//        // 校验批次号和样片表是否存在
//        ResponseResult<?> checkBatchResult = checkBatch(batchUid);
//        if (checkBatchResult != null) {
//            return checkBatchResult;
//        }
//
//        // 样片表名称
//        String tbSampleName = getBatchIdAndTbSampleName(batchUid).get("tbSampleName");
//
//        // 校验日志任务ID是否存在
//        if (sampleDao.existsSampleByLogTaskId(tbSampleName, logTaskId) == 0) {
//            return ResponseResult.error(ResponseEnum.DATA_NOT_EXISTS, "日志任务ID不存在");
//        }
//
//        // 1. 检测Redis中日志任务ID是否存在
//        Boolean exists = stringRedisTemplate.hasKey(logTaskId);
//        if (Boolean.FALSE.equals(exists)) {
//            return ResponseResult.error(ResponseEnum.DATA_NOT_EXISTS, "日志任务ID已失效");
//        }
//
//        // 2. 从 Redis 中获取对象列表
//        String jsonValue = stringRedisTemplate.opsForValue().get(logTaskId);
//        if (jsonValue == null) {
//            return ResponseResult.error(ResponseEnum.DATA_NOT_EXISTS, "分片数据不存在");
//        }
//
//        List<LogChunkInfoDto> logChunkInfoList;
//        try {
//            logChunkInfoList = objectMapper.readValue(
//                    jsonValue, new TypeReference<List<LogChunkInfoDto>>() {
//                    });
//        } catch (JsonProcessingException e) {
//            return ResponseResult.error(ResponseEnum.INTERNAL_SERVER_ERROR);
//        }
//
//        // 3. 判断分片ID是否存在
//        boolean isExists = logChunkInfoList.stream()
//                .anyMatch(logChunkInfo -> chunkId.equals(logChunkInfo.getChunkId()));
//
//        if (!isExists) {
//            return ResponseResult.error(ResponseEnum.DATA_NOT_EXISTS, "分片ID不存在");
//        }
//
//        // 4. 判断分片ID的状态是否为已上传
//        Optional<LogChunkInfoDto> logChunkInfoOpt = logChunkInfoList.stream()
//                .filter(logChunkInfo -> chunkId.equals(logChunkInfo.getChunkId()))
//                .findFirst();
//
//        if (logChunkInfoOpt.isPresent() && logChunkInfoOpt.get().getChunkUploadStatus() == UploadStatusEnum.DONE.getValue()) {
//            return ResponseResult.success(ResponseEnum.DATA_ALREADY_EXISTS, "该分片已上传");
//        }
//
//        // 5. 分片保存
//        try {
//            uploadChunk(logTaskId, chunkId, chunkFile);
//        } catch (IOException e) {
//            return ResponseResult.error(ResponseEnum.UPLOAD_ERROR, "分片保存失败");
//        }
//
//        // 6. 更新分片上传状态
//        logChunkInfoList.stream()
//                .filter(logChunkInfo -> chunkId.equals(logChunkInfo.getChunkId()))
//                .forEach(logChunkInfo -> logChunkInfo.setChunkUploadStatus(UploadStatusEnum.DONE.getValue()));
//
//        // 7. 将更新后的分片信息序列化回Redis（不能改变有效期）
//        String updatedJsonValue;
//        try {
//            updatedJsonValue = objectMapper.writeValueAsString(logChunkInfoList);
//            Long expireTime = stringRedisTemplate.getExpire(logTaskId);
//
//            if (expireTime != null && expireTime > 0) {
//                stringRedisTemplate.opsForValue().set(logTaskId, updatedJsonValue, expireTime, TimeUnit.SECONDS);
//            } else {
//                return ResponseResult.error(ResponseEnum.INTERNAL_SERVER_ERROR, "日志任务ID已失效");
//            }
//        } catch (JsonProcessingException e) {
//            return ResponseResult.error(ResponseEnum.INTERNAL_SERVER_ERROR);
//        }
//
//        // 8. 获取所有分片的上传状态，判断是否全部已上传
//        boolean allChunksUploadStatus = logChunkInfoList.stream()
//                .allMatch(logChunkInfo -> logChunkInfo.getChunkUploadStatus() == UploadStatusEnum.DONE.getValue());
//
//        // 响应数据
//        Map<String, Object> data = new HashMap<>();
//        data.put("allChunksUploadStatus", allChunksUploadStatus);  // 所有分片是否已上传
//        data.put("chunkInfoList", logChunkInfoList);  // 所有分片信息
//        return ResponseResult.success(ResponseEnum.UPLOAD_SUCCESS, data);
//    }


//    /**
//     * 合并日志分片
//     */
//    public ResponseResult<?> mergeLog(Long batchUid, String logTaskId) {
//        // 校验批次号和样片表是否存在
//        ResponseResult<?> checkBatchResult = checkBatch(batchUid);
//        if (checkBatchResult != null) {
//            return checkBatchResult;
//        }
//
//        // 样片表名称
//        String tbSampleName = getBatchIdAndTbSampleName(batchUid).get("tbSampleName");
//
//        // 校验日志任务ID是否存在
//        if (sampleDao.existsSampleByLogTaskId(tbSampleName, logTaskId) == 0) {
//            return ResponseResult.error(ResponseEnum.DATA_NOT_EXISTS, "日志任务ID不存在");
//        }
//
//        // 获取样片量产信息
//        SampleEntity sampleEntity = sampleDao.selectSampleByLogTaskId(tbSampleName, logTaskId);
//
//        // 1. 检测Redis中日志任务ID是否存在
//        Boolean exists = stringRedisTemplate.hasKey(logTaskId);
//        if (Boolean.FALSE.equals(exists)) {
//            return ResponseResult.error(ResponseEnum.DATA_NOT_EXISTS, "日志任务ID已失效");
//        }
//
//        // 2. 从 Redis 中获取对象列表
//        String jsonValue = stringRedisTemplate.opsForValue().get(logTaskId);
//        if (jsonValue == null) {
//            return ResponseResult.error(ResponseEnum.DATA_NOT_EXISTS, "分片数据不存在");
//        }
//
//        List<LogChunkInfoDto> logChunkInfoList;
//        try {
//            logChunkInfoList = objectMapper.readValue(
//                    jsonValue, new TypeReference<List<LogChunkInfoDto>>() {
//                    });
//        } catch (JsonProcessingException e) {
//            return ResponseResult.error(ResponseEnum.INTERNAL_SERVER_ERROR);
//        }
//
//        // 3. 获取所有分片的上传状态，判断是否全部已上传
//        boolean allChunksUploadStatus = logChunkInfoList.stream()
//                .allMatch(logChunkInfo -> logChunkInfo.getChunkUploadStatus() == UploadStatusEnum.DONE.getValue());
//
//        if (!allChunksUploadStatus) {
//            return ResponseResult.error(ResponseEnum.UPLOAD_ERROR, "所有分片均上传完才可合并");
//        }
//
//        // 4. 获取文件存放地址
//        String mergedFilePath = getAvailableRepoPath() + File.separator;
//
//        // 5. 执行合并操作
//        mergeChunks(logTaskId, logChunkInfoList, mergedFilePath);
//
//        // 6. 删除分片
//        deleteChunks(logTaskId);
//
//        // 返回结果
//        return ResponseResult.success(ResponseEnum.MERGE_SUCCESS);
//    }


}
