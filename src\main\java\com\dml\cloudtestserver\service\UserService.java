package com.dml.cloudtestserver.service;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.crypto.SecureUtil;
import com.dml.cloudtestserver.dao.*;
import com.dml.cloudtestserver.model.assignable_permission.AssignablePermissionEntity;
import com.dml.cloudtestserver.model.auth.UpdatePasswordDto;
import com.dml.cloudtestserver.model.auth.UpdatePasswordReq;
import com.dml.cloudtestserver.model.batch.BatchRes;
import com.dml.cloudtestserver.model.permission.PermissionEntity;
import com.dml.cloudtestserver.model.user.*;
import com.dml.cloudtestserver.model.user_power.UserPowerDto;
import com.dml.cloudtestserver.util.constants.Constants;
import com.dml.cloudtestserver.util.constants.PowerScopeEnum;
import com.dml.cloudtestserver.util.constants.UserCategoryEnum;
import com.dml.cloudtestserver.util.response.ResponseEnum;
import com.dml.cloudtestserver.util.response.ResponseResult;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class UserService {

    @Autowired
    private UserDao userDao;

    @Autowired
    private PermissionDao permissionDao;

    @Autowired
    private UserPowerDao userPowerDao;

    @Autowired
    private AssignablePermissionDao assignablePermissionDao;

    @Autowired
    private BatchDao batchDao;

    /**
     * 添加用户
     */
    public ResponseResult<?> addUser(UserReq userReq) {
        // 数据
        Long createUserUid = StpUtil.getLoginIdAsLong(); // 创建者UID
        String account = userReq.getAccount();
        Integer status = userReq.getStatus();
        String password = Constants.DEFAULT_USER_PASSWORD;
        String passwordMD5 = SecureUtil.md5(password);  // 对密码加密

        // 判断创建者
        UserEntity createUserEntity = userDao.selectUser(createUserUid);
        if (createUserEntity == null) {
            return ResponseResult.error(ResponseEnum.DATA_NOT_EXISTS, "创建者不存在");
        }
        Integer createUserCategory = createUserEntity.getCategory();

        // 判断账号是否已存在
        if (userDao.existsAccount(account)) {
            return ResponseResult.error(ResponseEnum.DATA_ALREADY_EXISTS, "账号已存在");
        }

        // 数据对象
        Long uid = IdUtil.getSnowflakeNextId();
        int scope;
        UserInsertDto userInsertDto;
        if (createUserCategory.equals(UserCategoryEnum.SUPER_ADMIN.getValue())) {
            userInsertDto = new UserInsertDto(
                    uid, createUserUid, account, passwordMD5, status, UserCategoryEnum.ADMIN.getValue());
            scope = UserCategoryEnum.SUPER_ADMIN.getValue();

        } else {
            userInsertDto = new UserInsertDto(
                    uid, createUserUid, account, passwordMD5, status, UserCategoryEnum.USER.getValue());
            scope = UserCategoryEnum.ADMIN.getValue();
        }

        // 插入
        int affectedRows = userDao.insertUser(userInsertDto);
        if (affectedRows == 0) {
            return ResponseResult.error(ResponseEnum.ADD_ERROR);
        }

        // 赋予可拥有的所有权限
        List<AssignablePermissionEntity> assignablePermissionEntityList =
                assignablePermissionDao.queryAllAssignablePermission(scope);
        List<Long> permissionUidList = new ArrayList<>();
        for (AssignablePermissionEntity entity : assignablePermissionEntityList) {
            permissionUidList.add(entity.getPermissionUid());
        }
        for (Long permissionUid : permissionUidList) {
            if (permissionDao.existsUid(permissionUid)) {
                userPowerDao.insertUserPower(
                        new UserPowerDto(IdUtil.getSnowflakeNextId(), uid, PowerScopeEnum.PERMISSION.getValue(), permissionUid));
            }
        }

        // 赋予所有批次
        List<BatchRes> batchResList = batchDao.selectAllBatch();
        List<Long> batchUidList = new ArrayList<>();
        for (BatchRes entity : batchResList) {
            batchUidList.add(entity.getUid());
        }
        for (Long batchUid : batchUidList) {
            if (batchDao.existsUid(batchUid)) {
                userPowerDao.insertUserPower(
                        new UserPowerDto(IdUtil.getSnowflakeNextId(), uid, PowerScopeEnum.BATCH.getValue(), batchUid));
            }
        }

        // 查询数据
        UserEntity userEntity = userDao.selectUser(uid);
        UserEntity userEntity2 = userDao.selectUser(createUserUid);
        String parentAccount = "";
        if (userEntity2 != null) {
            parentAccount = userEntity2.getAccount();
        }

        UserRes userRes = new UserRes(
                userEntity.getUid(),
                userEntity.getCreateUserUid(),
                parentAccount,
                userEntity.getAccount(),
                userEntity.getStatus(),
                userEntity.getCategory(),
                userEntity.getCreatedTime(),
                userEntity.getUpdatedTime());

        // 返回结果
        return ResponseResult.success(ResponseEnum.ADD_SUCCESS, userRes);
    }

    /**
     * 删除用户
     */
    public ResponseResult<?> deleteUser(Long uid) {
        // 检查用户是否存在
        UserEntity userEntity = userDao.selectUser(uid);
        if (userEntity == null) {
            return ResponseResult.error(ResponseEnum.DATA_NOT_EXISTS, "账号不存在");
        }

        // 从Token取出操作者的用户UID，判断是否为被操作者用户的创建者
        Long createUserUid = StpUtil.getLoginIdAsLong();
        Long oldCreateUserUid = userEntity.getCreateUserUid();
        if (!createUserUid.equals(oldCreateUserUid)) {
            return ResponseResult.error(ResponseEnum.NOT_CREATOR);
        }

        // 删除用户权力
        userPowerDao.deleteUserPowerList(uid);

        // 删除用户数据
        int affectedRows = userDao.deleteUser(uid);
        if (affectedRows == 0) {
            return ResponseResult.error(ResponseEnum.DELETE_ERROR);
        }

        // 返回结果
        return ResponseResult.success(ResponseEnum.DELETE_SUCCESS);
    }

    /**
     * 更改用户
     */
    public ResponseResult<?> updateUser(Long uid, UserUpdateReq userUpdateReq) {
        // 检查用户是否存在
        UserEntity userEntity = userDao.selectUser(uid);
        if (userEntity == null) {
            return ResponseResult.error(ResponseEnum.DATA_NOT_EXISTS, "账号不存在");
        }

        // 旧数据
        Long oldCreateUserUid = userEntity.getCreateUserUid();
        String oldAccount = userEntity.getAccount();
        Integer oldStatus = userEntity.getStatus();
        UserEntity oldCreateUserEntity = userDao.selectUser(oldCreateUserUid);

        // 新数据
        String newAccount = userUpdateReq.getAccount();
        Integer newStatus = userUpdateReq.getStatus();

        // 从Token取出操作者的用户UID，判断是否为被操作者用户的创建者
        Long createUserUid = StpUtil.getLoginIdAsLong();
        if (!createUserUid.equals(oldCreateUserUid)) {
            return ResponseResult.error(ResponseEnum.NOT_CREATOR);
        }

        // 判断是否数据未改变
        if ((newAccount == null || oldAccount.equals(newAccount)) &&
                (newStatus == null || oldStatus.equals(newStatus))) {
            return ResponseResult.error(ResponseEnum.DATA_NOT_CHANGE);
        }

        // 判断账号是否存在
        if (newAccount != null && !newAccount.equals(oldAccount) && userDao.existsAccount(newAccount)) {
            return ResponseResult.error(ResponseEnum.DATA_ALREADY_EXISTS, "账号已存在");
        }

        // 更新数据
        UserUpdateDto userUpdateDto = new UserUpdateDto(uid, newAccount, newStatus);
        int affectedRows = userDao.updateUser(userUpdateDto);
        if (affectedRows == 0) {
            return ResponseResult.error(ResponseEnum.UPDATE_ERROR);
        }

        // 返回结果
        UserRes userRes = new UserRes(
                userEntity.getUid(),
                userEntity.getCreateUserUid(),
                oldCreateUserEntity.getAccount(),
                userUpdateReq.getAccount(),
                userUpdateReq.getStatus(),
                userEntity.getCategory(),
                userEntity.getCreatedTime(),
                userEntity.getUpdatedTime());

        return ResponseResult.success(ResponseEnum.UPDATE_SUCCESS, userRes);
    }

    /**
     * 更改密码
     */
    public ResponseResult<?> updatePassword(Long uid, UpdatePasswordReq updatePasswordReq) {
        String oldPassword = updatePasswordReq.getOldPassword();
        String oldPasswordMd5 = SecureUtil.md5(oldPassword);
        String newPassword = updatePasswordReq.getNewPassword();
        String newPasswordMd5 = SecureUtil.md5(newPassword);

        Long userId = StpUtil.getLoginIdAsLong();
        UserEntity userEntity = userDao.selectUser(userId);

        // 判断账号是否已存在
        if (userEntity == null) {
            return ResponseResult.error(ResponseEnum.DATA_NOT_EXISTS, "账号不存在");
        }

        // 是否为本人操作
        if (!userId.equals(uid)) {
            return ResponseResult.error(ResponseEnum.ONLY_OWNER_CAN_OPERATE);
        }

        // 判断密码是否正确
        if (!oldPasswordMd5.equals(userEntity.getPassword())) {
            return ResponseResult.error(ResponseEnum.PASSWORD_ERROR);
        }

        // 更新数据
        UpdatePasswordDto updatePasswordDto = new UpdatePasswordDto(userEntity.getUid(), newPasswordMd5);
        int affectedRows = userDao.updatePassword(updatePasswordDto);
        if (affectedRows == 0) {
            return ResponseResult.error(ResponseEnum.UPDATE_ERROR);
        }

        return ResponseResult.success(ResponseEnum.UPDATE_SUCCESS, "密码更新成功");
    }

    /**
     * 重置密码
     */
    public ResponseResult<?> resetPassword(Long userUid) {
        // 判断账号是否已存在
        UserEntity userEntity = userDao.selectUser(userUid);
        if (userEntity == null) {
            return ResponseResult.error(ResponseEnum.DATA_NOT_EXISTS, "账号不存在");
        }

        // 判断是否为创建者创建的用户
        Long createUserUid = StpUtil.getLoginIdAsLong();
        if (!userEntity.getCreateUserUid().equals(createUserUid)) {
            return ResponseResult.error(ResponseEnum.NOT_CREATOR);
        }

        // 检查当前密码是否与新密码相同
        String newPasswordMd5 = SecureUtil.md5(Constants.DEFAULT_USER_PASSWORD);
        if (userEntity.getPassword().equals(newPasswordMd5)) {
            return ResponseResult.error(ResponseEnum.DATA_NOT_CHANGE, "密码已是默认密码，无需重置");
        }

        // 更新数据
        UpdatePasswordDto updatePasswordDto = new UpdatePasswordDto(userEntity.getUid(), newPasswordMd5);
        int affectedRows = userDao.updatePassword(updatePasswordDto);
        if (affectedRows == 0) {
            return ResponseResult.error(ResponseEnum.UPDATE_ERROR, "密码重置失败");
        }

        return ResponseResult.success(ResponseEnum.UPDATE_SUCCESS, "密码重置成功");
    }

    /**
     * 查询单个用户
     */
    public ResponseResult<?> queryUser(Long uid) {
        // 查询数据
        UserEntity userEntity = userDao.selectUser(uid);
        if (userEntity == null) {
            return ResponseResult.error(ResponseEnum.DATA_NOT_EXISTS, "账号不存在");
        }

        Long createUserUid = userEntity.getCreateUserUid();
        UserEntity userEntity2 = userDao.selectUser(createUserUid);
        String parentAccount = "";
        if (userEntity2 != null) {
            parentAccount = userEntity2.getAccount();
        }

        UserRes userRes = new UserRes(
                userEntity.getUid(),
                userEntity.getCreateUserUid(),
                parentAccount,
                userEntity.getAccount(),
                userEntity.getStatus(),
                userEntity.getCategory(),
                userEntity.getCreatedTime(),
                userEntity.getUpdatedTime());

        // 返回结果
        return ResponseResult.success(ResponseEnum.QUERY_SUCCESS, userRes);
    }

    /**
     * 批量查询用户
     */
    public ResponseResult<?> queryUserList(
            int pageNum,
            int pageSize,
            String createUserAccount,
            List<Integer> category,
            String account,
            List<Integer> status) {
        // 开启分页
        PageHelper.startPage(pageNum, pageSize);

        // 查询数据
        List<UserEntity> userEntityList = userDao.selectUserList(createUserAccount, category, account, status);

        // 获取用户UID
        Long loginUserUid = StpUtil.getLoginIdAsLong();
        UserEntity loginUserEntity = userDao.selectUser(loginUserUid);
        Integer loginUserCategory = loginUserEntity.getCategory();

        // 处理数据，将 UserEntity 转换为 UserRes，过滤掉密码
        List<UserRes> userResList = new ArrayList<>();
        for (UserEntity userEntity : userEntityList) {
            Long createUserUid = userEntity.getCreateUserUid();

            String parentAccount = "";
            UserEntity userEntity2 = userDao.selectUser(createUserUid);
            if (userEntity2 != null) {
                parentAccount = userEntity2.getAccount();
            }

            UserRes userRes = new UserRes(
                    userEntity.getUid(),
                    createUserUid,
                    parentAccount,
                    userEntity.getAccount(),
                    userEntity.getStatus(),
                    userEntity.getCategory(),
                    userEntity.getCreatedTime(),
                    userEntity.getUpdatedTime());

            // 不同用户类别用户列表的权限不同
            if (loginUserCategory == UserCategoryEnum.SUPER_ADMIN.getValue()) {
                userResList.add(userRes);
            } else if (loginUserCategory == UserCategoryEnum.ADMIN.getValue()) {
                PermissionEntity permissionEntity = permissionDao.selectPermissionByFuncCode("user-query");
                Long permissionUid = permissionEntity.getUid();
                boolean flag = userPowerDao.existsUidAndPowerId(loginUserUid, permissionUid);

                // 有查询用户的权限
                if (flag) {
                    if (Objects.equals(createUserUid, loginUserUid)) {
                        userResList.add(userRes);
                    }
                } else {
                    return ResponseResult.error(ResponseEnum.NO_PERMISSION_ERROR);
                }
            } else {
                return ResponseResult.error(ResponseEnum.NO_PERMISSION_ERROR);
            }
        }

        // 创建PageInfo<UserRes>对象
        PageInfo<UserRes> pageInfo = new PageInfo<>(userResList);
        pageInfo.setTotal(((Page<UserEntity>) userEntityList).getTotal()); // 获取总数并设置

        // 返回结果
        return ResponseResult.success(ResponseEnum.QUERY_SUCCESS, pageInfo);
    }

}
