package com.dml.cloudtestserver.dao;

import com.dml.cloudtestserver.model.user_power.UserPowerDto;
import com.dml.cloudtestserver.model.user_power.UserPowerEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface UserPowerDao {

    // 批量删除
    int deleteUserPowerList(Long userUid);

    // 删除指定批次所有用户的权力
    int deleteUserPowerListByBatchUid(Long batchUid);

    // 查询用户所有权力
    List<UserPowerEntity> selectUserAllPower(
            @Param("userUid") Long userUid, @Param("scope") Integer scope);

    // 插入用户权力
    int insertUserPower(UserPowerDto userPowerDto);

    // UID和权力UID是否存在
    boolean existsUidAndPowerId(
            @Param("userUid") Long userUid, @Param("powerUid") Long powerUid);

    // 查询用户下所有权限UID
    List<Long> selectAllPermissionUid(Long userUid);

    // 查询用户下所有批次UID
    List<Long> selectAllBatchUid(Long userUid);

}
