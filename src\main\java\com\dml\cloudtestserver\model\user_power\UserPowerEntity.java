package com.dml.cloudtestserver.model.user_power;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户-权力表实体
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserPowerEntity implements Serializable {

    private static final long serialVersionUID = 7788227786585355947L;

    /**
     * UID
     */
    private Long uid;


    /**
     * 用户UID
     */
    private Long userUid;


    /**
     * 权力范围
     */
    private Integer scope;


    /**
     * 权力UID
     */
    private Long powerUid;


    /**
     * 授权时间
     */
    private Date grantedTime;
}
