package com.dml.cloudtestserver.controller.handler;

import com.dml.cloudtestserver.util.properties.CustomProperties;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Slf4j
public abstract class BaseWebSocketHandler extends TextWebSocketHandler {

    private ScheduledExecutorService scheduler;  // 创建调度器
    private volatile boolean isSchedulerShutdown = true;  // 标记调度器是否已关闭

    @Autowired
    private CustomProperties customProperties;

    protected void initializeScheduler() {
        // 初始化调度器
        scheduler = Executors.newScheduledThreadPool(1);
        isSchedulerShutdown = false;
    }

    @Override
    public void afterConnectionEstablished(WebSocketSession session) {
        if (isSchedulerShutdown) {
            initializeScheduler(); // 重新初始化调度器
        }

        // 在 WebSocket 连接建立后启动定时任务
        scheduler.scheduleAtFixedRate(() -> {
            if (isSchedulerShutdown) {
                return; // 如果调度器已关闭，则不再执行任务
            }
            try {
                if (session.isOpen()) {
                    Map<String, Object> currentData = new ConcurrentHashMap<>(getData());
                    String jsonData = new ObjectMapper().writeValueAsString(currentData);
                    session.sendMessage(new TextMessage(jsonData));
                }
            } catch (Exception e) {
                log.error("Error sending message: {}", e.getMessage());
            }
        }, 0, getUpdateFrequency(), TimeUnit.SECONDS);
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) {
        // 在 WebSocket 连接关闭后标记为已关闭
        isSchedulerShutdown = true;

        // 如果调度器未关闭，则关闭调度器
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
        }
    }

    // 抽象方法，子类需要实现
    protected abstract Map<String, Object> getData();

    // 提供默认实现
    protected long getUpdateFrequency() {
        return customProperties.getWebsocketUpdateFrequency();
    }
}