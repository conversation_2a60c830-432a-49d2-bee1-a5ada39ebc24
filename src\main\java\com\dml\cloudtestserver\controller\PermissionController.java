package com.dml.cloudtestserver.controller;


import com.dml.cloudtestserver.service.PermissionService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "权限管理")
@RestController
@RequestMapping("/api/permission")
public class PermissionController {

    @Autowired
    private PermissionService permissionService;


//    @Operation(summary = "添加权限")
//    @PostMapping("")
//    @SaCheckPermission("permission-add")
//    public ResponseResult<?> addPermission(
//            @Schema(implementation = PermissionReq.class) @Valid @RequestBody PermissionReq permissionReq) {
//        return permissionService.addPermission(permissionReq);
//    }

//    @Operation(summary = "删除权限")
//    @DeleteMapping("/{uid}")
//    @SaCheckPermission("permission-delete")
//    public ResponseResult<?> deletePermission(@PathVariable Long uid) {
//        return permissionService.deletePermission(uid);
//    }

//    @Operation(summary = "更改权限")
//    @PutMapping("/{uid}")
//    @SaCheckPermission("permission-update")
//    public ResponseResult<?> updatePermission(
//            @PathVariable Long uid,
//            @Schema(implementation = PermissionReq.class) @Valid @RequestBody PermissionReq permissionReq) {
//        return permissionService.updatePermission(uid, permissionReq);
//    }

//    @Operation(summary = "查询权限")
//    @GetMapping("/{uid}")
//    @SaCheckPermission("permission-query")
//    public ResponseResult<?> queryPermission(@PathVariable Long uid) {
//        return permissionService.queryPermission(uid);
//    }


//    @Operation(summary = "查询所有权限")
//    @GetMapping("")
//    @SaCheckPermission("permission-query")
//    public ResponseResult<?> queryAllPermission(
//            @Parameter(description = "权限名称") @RequestParam(value = "name", required = false) String name
//    ) {
//        return permissionService.queryAllPermission(name);
//    }
}