package com.dml.cloudtestserver.util.response;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResponseResult<T> {
    private int code; // 响应码
    private String name; // 名称
    private String msg; // 响应消息
    private T data; // 数据

    /*
        构建成功响应的方法
     */

    public static <T> ResponseResult<T> success(String msg) {
        return new ResponseResult<>(ResponseEnum.SUCCESS.getCode(), ResponseEnum.SUCCESS.getName(), msg, null);
    }

    public static <T> ResponseResult<T> success(T data) {
        return new ResponseResult<>(ResponseEnum.SUCCESS.getCode(), ResponseEnum.SUCCESS.getName(), ResponseEnum.SUCCESS.getMsg(), data);
    }

    public static <T> ResponseResult<T> success(String msg, T data) {
        return new ResponseResult<>(ResponseEnum.SUCCESS.getCode(), ResponseEnum.SUCCESS.getName(), msg, data);
    }

    public static <T> ResponseResult<T> success(ResponseEnum responseEnum) {
        return new ResponseResult<>(responseEnum.getCode(), responseEnum.getName(), responseEnum.getMsg(), null);
    }

    public static <T> ResponseResult<T> success(ResponseEnum responseEnum, String msg) {
        return new ResponseResult<>(responseEnum.getCode(), responseEnum.getName(), msg, null);
    }

    public static <T> ResponseResult<T> success(ResponseEnum responseEnum, T data) {
        return new ResponseResult<>(responseEnum.getCode(), responseEnum.getName(), responseEnum.getMsg(), data);
    }

    /*
        构建失败响应的方法
     */

    public static <T> ResponseResult<T> error(ResponseEnum responseEnum) {
        return new ResponseResult<>(responseEnum.getCode(), responseEnum.getName(), responseEnum.getMsg(), null);
    }

    public static <T> ResponseResult<T> error(ResponseEnum responseEnum, String msg) {
        return new ResponseResult<>(responseEnum.getCode(), responseEnum.getName(), msg, null);
    }

    public static <T> ResponseResult<T> error(ResponseEnum responseEnum, T data) {
        return new ResponseResult<>(responseEnum.getCode(), responseEnum.getName(), responseEnum.getMsg(), data);
    }

}
