<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dml.cloudtestserver.dao.TableDao">


    <select id="countRowsInTable" parameterType="String" resultType="Integer">
        SELECT COUNT(*)
        FROM ${tableName}
    </select>


    <update id="renameTable">
        ALTER TABLE ${oldTableName} RENAME TO ${newTableName}
    </update>


    <select id="existTable" parameterType="String" resultType="Integer">
        select count(*)
        from information_schema.TABLES
        where table_schema = DATABASE()
          AND table_name = #{tableName}
    </select>


    <update id="dropTable" parameterType="String">
        DROP TABLE ${tableName}
    </update>


    <update id="createSampleTable" parameterType="String">
        CREATE TABLE ${tableName}
        (
            `uid`                bigint UNSIGNED       NOT NULL COMMENT 'UID',
            `batch_uid`          bigint UNSIGNED       NOT NULL COMMENT '批次UID',
            `sample_id`          varchar(50) CHARACTER
                SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '样片号（flash_uid）',
            `flash_type`         varchar(50) CHARACTER
                SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '闪存类型',
            `flash_num`          int                   NOT NULL COMMENT '闪存数量',
            `product_type`       tinyint               NOT NULL COMMENT '产品类型: 1-T卡、2-U盘、3-SSD',
            `controller_type`    varchar(50) CHARACTER
                SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主控类型',
            `interface_type`     varchar(50) CHARACTER
                SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '接口类型',
            `capacity`           int UNSIGNED          NOT NULL COMMENT '容量(MB)',
            `capacity_mode`      varchar(50) CHARACTER
                SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '容量模式',
            `speed_type`         varchar(50) CHARACTER
                SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '速度标记',
            `production_mode`    varchar(50) CHARACTER
                SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '量产模式',
            `production_version` varchar(200) CHARACTER
                SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '量产版本',
            `production_result`  tinyint UNSIGNED      NOT NULL COMMENT '量产结果: 0-失败，1-成功',
            `error_code`         int                   NOT NULL COMMENT '错误码',
            `os_platform`        varchar(50) CHARACTER
                SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作系统',
            `log_dir`            varchar(200) CHARACTER
                SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '日志目录',
            `log_name`           varchar(200) CHARACTER
                SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '日志文件名称',
            `log_upload_status`  tinyint UNSIGNED      NOT NULL COMMENT '日志上传状态: 0-未开始 1-成功 2-失败',
            `created_time`       timestamp(3)          NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间'
        ) ENGINE = InnoDB
          DEFAULT CHARSET = utf8mb4
          COLLATE = utf8mb4_general_ci COMMENT ='样片表';
    </update>


    <update id="addSampleTableIndex" parameterType="String">
        ALTER TABLE ${tableName}
            ADD PRIMARY KEY (`uid`),
            ADD UNIQUE KEY `udx_log_name` (`log_name`),
            ADD KEY `idx_sample_id` (`sample_id`),
            ADD KEY `idx_production_type` (`product_type`),
            ADD KEY `idx_production_result` (`production_result`),
            ADD KEY `idx_log_upload_status` (`log_upload_status`),
            ADD KEY `idx_created_time` (`created_time`),
            ADD KEY `idx_sample_id_created_time` (`sample_id`, `created_time`);
    </update>


</mapper>