package com.dml.cloudtestserver.util.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * SpringDoc 配置类，用于生成 OpenAPI 文档。
 * 这个类包含两个 Bean 配置：
 * 1. `customOpenAPI`：配置 OpenAPI 文档的基本信息，如标题、版本和描述。
 * 2. `publicApi`：定义一个 API 分组，并设置路径匹配规则来控制哪些接口会被包括在该分组中。
 */
@Configuration
public class SpringDocConfig {


    /**
     * 配置 OpenAPI 文档的基本信息。
     *
     * @return OpenAPI 实例，包含 API 文档的基本信息。
     */
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("研发制造测试管理平台 API") // API 文档的标题
                        .version("1.0") // API 文档的版本
                        .description("Spring Doc API 自动化生成文档")); // API 文档的描述
    }


}
