package com.dml.cloudtestserver.dao;

import com.dml.cloudtestserver.model.assignable_permission.AssignablePermissionEntity;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface AssignablePermissionDao {

    // 查询可分配权限列表
    List<AssignablePermissionEntity> queryAllAssignablePermission(Integer userCategory);

    // 批量删除可分配权限列表
    int deleteAssignablePermissionList(Integer userCategory);

    // 插入可分配权限
    int insertAssignablePermission(AssignablePermissionEntity assignablePermissionEntity);

}
