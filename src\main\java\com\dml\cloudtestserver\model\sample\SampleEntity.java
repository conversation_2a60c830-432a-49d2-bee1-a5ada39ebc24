package com.dml.cloudtestserver.model.sample;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 样片表实体
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SampleEntity implements Serializable {

    private static final long serialVersionUID = -3428838709362002505L;

    /**
     * UID
     */
    private Long uid;

    /**
     * 批次UID
     */
    private Long batchUid;

    /**
     * 样片号（flash_uid）
     */
    private String sampleId;

    /**
     * 闪存类型
     */
    private String flashType;

    /**
     * 闪存数量
     */
    private Integer flashNum;

    /**
     * 产品类型
     */
    private Integer productType;

    /**
     * 主控类型
     */
    private String controllerType;

    /**
     * 接口类型
     */
    private String interfaceType;

    /**
     * 容量(MB)
     */
    private Integer capacity;

    /**
     * 容量模式
     */
    private String capacityMode;

    /**
     * 速度标记
     */
    private String speedType;

    /**
     * 量产模式
     */
    private String productionMode;

    /**
     * 量产版本
     */
    private String productionVersion;

    /**
     * 量产结果
     */
    private Integer productionResult;

    /**
     * 错误码
     */
    private Integer errorCode;

    /**
     * 操作系统
     */
    private String osPlatform;

    /**
     * 日志目录
     */
    private String logDir;

    /**
     * 日志名称
     */
    private String logName;

    /**
     * 日志上传状态: 0-未开始 1-成功 2-失败
     */
    private Integer logUploadStatus;

    /**
     * 创建时间
     */
    private Date createdTime;
}
