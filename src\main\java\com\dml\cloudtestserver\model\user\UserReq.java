package com.dml.cloudtestserver.model.user;

import com.dml.cloudtestserver.util.constants.Constants;
import com.dml.cloudtestserver.util.constants.UserStatusEnum;
import com.dml.cloudtestserver.util.validator.ValidEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;


@Schema(description = "UserReq 用户请求模型")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserReq implements Serializable {

    private static final long serialVersionUID = 9217375727642776624L;


    /**
     * 账号
     */
    @Schema(description = "账号。仅支持中文、字母、数字、下划线(不能开头或结尾、不能仅有下划线), 长度1-20")
    @NotBlank(message = "账号不能为空")
    @Length(min = 1, max = 20)
    @Pattern(regexp = Constants.REGEX_CHINESE_LETTER_NUMBER_UNDERLINE, message = "仅支持中文、字母、数字、下划线")
    private String account;


    /**
     * 状态
     */
    @Schema(description = "状态。可选值：0-禁用、1-启动(默认)")
    @NotNull(message = "状态不能为空")
    @ValidEnum(enumClass = UserStatusEnum.class, message = "可选值：0-禁用、1-启动(默认)")
    private Integer status = UserStatusEnum.ENABLE.getValue();
}
