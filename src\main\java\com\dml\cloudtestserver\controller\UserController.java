package com.dml.cloudtestserver.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.dml.cloudtestserver.model.auth.UpdatePasswordReq;
import com.dml.cloudtestserver.model.user.UserReq;
import com.dml.cloudtestserver.model.user.UserUpdateReq;
import com.dml.cloudtestserver.service.UserService;
import com.dml.cloudtestserver.util.constants.Constants;
import com.dml.cloudtestserver.util.response.ResponseResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Tag(name = "用户管理")
@RestController
@RequestMapping("/api/user")
public class UserController {

    @Autowired
    private UserService userService;

    @Operation(summary = "添加用户")
    @PostMapping("")
    @SaCheckPermission("user-add")
    public ResponseResult<?> addUser(
            @Schema(implementation = UserReq.class)
            @Valid
            @RequestBody
            UserReq userReq) {
        return userService.addUser(userReq);
    }

    @Operation(summary = "删除用户")
    @DeleteMapping("/{uid}")
    @SaCheckPermission("user-delete")
    public ResponseResult<?> deleteUser(@PathVariable Long uid) {
        return userService.deleteUser(uid);
    }

    @Operation(summary = "更改用户")
    @PutMapping("/{uid}")
    @SaCheckPermission("user-update")
    public ResponseResult<?> updateBatch(
            @PathVariable
            Long uid,

            @Schema(implementation = UserReq.class)
            @Valid
            @RequestBody
            UserUpdateReq userUpdateReq) {
        return userService.updateUser(uid, userUpdateReq);
    }

    @Operation(summary = "更改密码")
    @PutMapping("updatePassword/{uid}")
    public ResponseResult<?> updatePassword(
            @PathVariable
            Long uid,

            @Schema(implementation = UpdatePasswordReq.class)
            @Valid
            @RequestBody
            UpdatePasswordReq updatePasswordReq) {
        return userService.updatePassword(uid, updatePasswordReq);
    }


    @Operation(summary = "重置密码")
    @PutMapping("resetPassword/{userUid}")
    @SaCheckPermission("user-reset-password")
    public ResponseResult<?> resetPassword(@PathVariable Long userUid) {
        return userService.resetPassword(userUid);
    }


//    @Operation(summary = "查询单个用户")
//    @GetMapping("/{uid}")
//    @SaCheckPermission("user-query")
//    public ResponseResult<?> queryUser(@PathVariable Long uid) {
//        return userService.queryUser(uid);
//    }

    @Operation(summary = "批量查询用户")
    @GetMapping("")
    @SaCheckPermission("user-query")
    public ResponseResult<?> queryUserList(
            @Parameter(description = "页码")
            @RequestParam(value = "pageNum", defaultValue = Constants.DEFAULT_PAGE_NUM)
            Integer pageNum,

            @Parameter(description = "每页数量")
            @RequestParam(value = "pageSize", defaultValue = Constants.DEFAULT_PAGE_SIZE)
            Integer pageSize,

//            @Parameter(description = "创建者") @RequestParam(value = "createUserAccount", required = false) String createUserAccount,
//            @Parameter(description = "类别") @RequestParam(value = "category", required = false) List<Integer> category,
            @Parameter(description = "账号")
            @RequestParam(value = "account", required = false)
            String account,

            @Parameter(description = "状态")
            @RequestParam(value = "status", required = false)
            List<Integer> status) {
        return userService.queryUserList(pageNum, pageSize, null, null, account, status);
    }

}
