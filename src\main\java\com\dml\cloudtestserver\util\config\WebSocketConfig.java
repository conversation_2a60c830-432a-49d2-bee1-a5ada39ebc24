package com.dml.cloudtestserver.util.config;

import com.dml.cloudtestserver.controller.handler.CoreDataHandler;
import com.dml.cloudtestserver.controller.handler.TodayDiffCapacitySampleNumDataHandler;
import com.dml.cloudtestserver.controller.handler.TodayHourlyProductionDataHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {
    private final CoreDataHandler coreDataHandler;
    private final TodayHourlyProductionDataHandler todayHourlyProductionDataHandler;
    private final TodayDiffCapacitySampleNumDataHandler todayDiffCapacitySampleNumDataHandler;

    @Autowired
    public WebSocketConfig(
            CoreDataHandler coreDataHandler,
            TodayHourlyProductionDataHandler todayHourlyProductionDataHandler,
            TodayDiffCapacitySampleNumDataHandler todayDiffCapacitySampleNumDataHandler) {
        this.coreDataHandler = coreDataHandler;
        this.todayHourlyProductionDataHandler = todayHourlyProductionDataHandler;
        this.todayDiffCapacitySampleNumDataHandler = todayDiffCapacitySampleNumDataHandler;
    }


    /**
     * 注册WebSocket处理器
     * 此方法用于将WebSocket处理器注册到指定的URL路径上，以便处理来自客户端的WebSocket连接请求
     * 通过WebSocketHandlerRegistry的addHandler方法，将特定的WebSocket处理器映射到对应的URL路径上
     * 并设置允许跨域访问，使得不同源的客户端可以接入WebSocket服务
     *
     * @param registry WebSocket处理器注册对象，用于注册WebSocket处理器和配置跨域访问
     */
    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(coreDataHandler, "/api/websocket/coreData")
                .setAllowedOrigins("*"); // 允许跨域

        registry.addHandler(todayHourlyProductionDataHandler, "/api/websocket/todayHourlyProductionData")
                .setAllowedOrigins("*"); // 允许跨域

        registry.addHandler(todayDiffCapacitySampleNumDataHandler, "/api/websocket/todayDiffCapacitySampleNumData")
                .setAllowedOrigins("*"); // 允许跨域

    }
}


