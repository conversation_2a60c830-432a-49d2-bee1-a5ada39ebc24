package com.dml.cloudtestserver.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.dml.cloudtestserver.model.auth.UpdatePasswordDto;
import com.dml.cloudtestserver.model.user.UserEntity;
import com.dml.cloudtestserver.model.user.UserInsertDto;
import com.dml.cloudtestserver.model.user.UserUpdateDto;

@Mapper
@Repository
public interface UserDao {

    // UID是否存在
    boolean existsUid(Long uid);

    // 账号是否存在
    boolean existsAccount(String account);

    // 插入用户
    int insertUser(UserInsertDto userInsertDto);

    // 删除用户
    int deleteUser(Long uid);

    // 更新用户
    int updateUser(UserUpdateDto userUpdateDto);

    // 更新密码
    int updatePassword(UpdatePasswordDto updatePasswordDto);

    // 查询用户
    UserEntity selectUser(Long uid);

    // 根据账号查询用户
    UserEntity selectUserByAccount(String account);

    // 查询用户列表
    List<UserEntity> selectUserList(
            @Param("createUserAccount") String createUserAccount,
            @Param("category") List<Integer> category,
            @Param("account") String account,
            @Param("status") List<Integer> status);

}
