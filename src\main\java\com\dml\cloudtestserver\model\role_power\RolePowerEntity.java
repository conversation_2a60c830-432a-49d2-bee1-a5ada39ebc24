package com.dml.cloudtestserver.model.role_power;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 角色-权力表实体
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RolePowerEntity implements Serializable {

    private static final long serialVersionUID = -2787818732094771392L;

    /**
     * UID
     */
    private Long uid;


    /**
     * 角色UID
     */
    private Long roleUid;


    /**
     * 权力范围
     */
    private Integer scope;


    /**
     * 权力UID
     */
    private Long powerUid;


    /**
     * 授权时间
     */
    private Date grantedTime;
}
