#!/bin/sh

# 启动MySQL
echo "Starting MySQL..."
cd /usr
/usr/bin/mariadbd-safe --datadir=/var/lib/mysql &

# 等待MySQL启动
sleep 10

# 启动Redis
echo "Starting Redis..."
redis-server --daemonize yes

# 等待Redis启动
sleep 5

# 启动Spring Boot应用
echo "Starting Spring Boot Application..."
cd /app
java -jar CloudTestServer-0.0.1-SNAPSHOT.jar \
  --spring.config.additional-location=/app/config/application-test-local.yml \
  --spring.profiles.active=test
