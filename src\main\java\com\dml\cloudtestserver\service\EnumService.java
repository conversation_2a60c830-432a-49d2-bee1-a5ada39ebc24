package com.dml.cloudtestserver.service;

import com.dml.cloudtestserver.util.constants.*;
import com.dml.cloudtestserver.util.response.ResponseEnum;
import com.dml.cloudtestserver.util.response.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
public class EnumService {

    // 定义一个包含所有枚举名称的列表
    private final List<String> enumNameList = Arrays.asList(
            "BatchStatusEnum",
            "PermissionScopeEnum",
            "PowerScopeEnum",
            "ProductionResultEnum",
            "ProductTypeEnum",
            "UploadStatusEnum",
            "UserCategoryEnum",
            "UserStatusEnum",
            "OsPlatformEnum");

    /**
     * 根据枚举名称获取对应的枚举值和描述信息
     *
     * @param enumName 枚举名称
     * @return 包含枚举值和描述的列表
     */
    private List<Map<String, Object>> getEnumValues(String enumName) {
        List<Map<String, Object>> resultList = new ArrayList<>();

        // 根据不同的枚举名称，获取对应的枚举值
        switch (enumName) {
            case "BatchStatusEnum":
                for (BatchStatusEnum item : BatchStatusEnum.values()) {
                    resultList.add(createEnumMap(item.getValue(), item.getDesc()));
                }
                break;

            case "PermissionScopeEnum":
                for (PermissionScopeEnum item : PermissionScopeEnum.values()) {
                    resultList.add(createEnumMap(item.getValue(), item.getDesc()));
                }
                break;

            case "PowerScopeEnum":
                for (PowerScopeEnum item : PowerScopeEnum.values()) {
                    resultList.add(createEnumMap(item.getValue(), item.getDesc()));
                }
                break;

            case "ProductionResultEnum":
                for (ProductionResultEnum item : ProductionResultEnum.values()) {
                    resultList.add(createEnumMap(item.getValue(), item.getDesc()));
                }
                break;

            case "ProductTypeEnum":
                for (ProductTypeEnum item : ProductTypeEnum.values()) {
                    resultList.add(createEnumMap(item.getValue(), item.getDesc()));
                }
                break;

            case "UploadStatusEnum":
                for (UploadStatusEnum item : UploadStatusEnum.values()) {
                    resultList.add(createEnumMap(item.getValue(), item.getDesc()));
                }
                break;

            case "UserCategoryEnum":
                for (UserCategoryEnum item : UserCategoryEnum.values()) {
                    resultList.add(createEnumMap(item.getValue(), item.getDesc()));
                }
                break;

            case "UserStatusEnum":
                for (UserStatusEnum item : UserStatusEnum.values()) {
                    resultList.add(createEnumMap(item.getValue(), item.getDesc()));
                }
                break;

            case "OsPlatform":
                for (OsPlatformEnum item : OsPlatformEnum.values()) {
                    resultList.add(createEnumMap(item.getValue(), item.getDesc()));
                }
                break;

            default:
                break; // 处理未知的枚举名称
        }
        return resultList;
    }

    /**
     * 创建枚举值和描述的映射
     *
     * @param value 枚举值
     * @param desc  枚举描述
     * @return 包含枚举值和描述的映射
     */
    private Map<String, Object> createEnumMap(Object value, String desc) {
        Map<String, Object> itemMap = new LinkedHashMap<>(); // 使用 LinkedHashMap 保持插入顺序
        itemMap.put("value", value);
        itemMap.put("desc", desc);
        return itemMap;
    }

    /**
     * 查询单个枚举信息
     *
     * @param enumName 枚举名称
     * @return 查询结果，包含该枚举的信息
     */
    public ResponseResult<?> queryEnum(String enumName) {
        // 检查枚举名称是否有效
        if (!enumNameList.contains(enumName)) {
            return ResponseResult.error(ResponseEnum.QUERY_ERROR, "未知枚举名称");
        }

        // 获取对应枚举的信息
        List<Map<String, Object>> resultList = getEnumValues(enumName);

        // 创建新的 Map 来封装 enumName 和 enumInfo
        Map<String, Object> responseMap = new LinkedHashMap<>();
        responseMap.put("enumName", enumName);
        responseMap.put("enumInfo", resultList);

        // 返回结果
        return ResponseResult.success(ResponseEnum.QUERY_SUCCESS, responseMap);
    }

    /**
     * 查询所有枚举信息
     *
     * @return 查询结果，包含所有枚举的信息
     */
    public ResponseResult<?> queryAllEnum() {
        List<Map<String, Object>> allEnumList = new ArrayList<>();

        // 遍历所有枚举类型
        for (String enumName : enumNameList) {
            Map<String, Object> enumMap = new LinkedHashMap<>(); // 使用 LinkedHashMap 保持插入顺序
            enumMap.put("enumName", enumName); // 添加枚举名称
            enumMap.put("enumInfo", getEnumValues(enumName)); // 添加对应的枚举信息
            allEnumList.add(enumMap);
        }

        // 返回结果
        return ResponseResult.success(ResponseEnum.QUERY_SUCCESS, allEnumList);
    }
}