package com.dml.cloudtestserver.controller.handler;

import com.dml.cloudtestserver.dao.BatchDao;
import com.dml.cloudtestserver.dao.SampleDao;
import com.dml.cloudtestserver.dao.TableDao;
import com.dml.cloudtestserver.model.batch.BatchRes;
import com.dml.cloudtestserver.util.constants.Constants;
import com.dml.cloudtestserver.util.response.ResponseEnum;
import com.dml.cloudtestserver.util.response.ResponseResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Service
public class CoreDataHandler extends BaseWebSocketHandler {

    @Autowired
    private BatchDao batchDao;

    @Autowired
    private SampleDao sampleDao;

    @Autowired
    private TableDao tableDao;

    @Override
    protected Map<String, Object> getData() {
        Map<String, Object> data = new ConcurrentHashMap<>();
        data.put("totalBatchCount", getBatchNum());
        data.put("totalSampleCount", getAllBatchTotalSampleNum());
        return data;
    }

    private int getBatchNum() {
        return batchDao.countBatchNum();
    }

    private int getAllBatchTotalSampleNum() {
        int totalSampleNum = 0;
        List<BatchRes> batchResList = batchDao.selectAllBatch();
        for (BatchRes batchRes : batchResList) {
            String tbSampleName = Constants.TB_SAMPLE_NAME_PREFIX + batchRes.getUid();
            if (tableDao.existTable(tbSampleName) > 0) {
                totalSampleNum += sampleDao.countSampleNum(tbSampleName);
            }

        }
        return totalSampleNum;
    }

}