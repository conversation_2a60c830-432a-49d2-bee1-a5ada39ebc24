package com.dml.cloudtestserver.model.sample;

import com.dml.cloudtestserver.util.constants.ProductTypeEnum;
import com.dml.cloudtestserver.util.constants.ProductionResultEnum;
import com.dml.cloudtestserver.util.validator.ValidEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SampleInsertReq implements Serializable {

    private static final long serialVersionUID = 6028049556959529166L;

    /**
     * 样片号（flash_uid）
     */
    @Schema(description = "样片号（flash_uid）。必须为 32 位数字和字母(大写)组合")
    @NotBlank(message = "样片号（flash_uid）不能为空")
    @Pattern(regexp = "^[A-Z0-9]{32}$", message = "样片号（flash_uid）必须为 32 位数字和字母(大写)组合")
    private String sampleId;

    /**
     * 闪存类型
     */
    @Schema(description = "闪存类型。长度不超过50")
    @NotBlank(message = "闪存类型不能为空")
    @Length(max = 50)
    private String flashType;

    /**
     * 闪存数量
     */
    @Schema(description = "闪存数量。")
    @NotNull(message = "闪存数量不能为空")
    private Integer flashNum;

    /**
     * 产品类型
     */
    @Schema(description = "产品类型。可选值：1-T卡、2-U盘、3-SSD")
    @NotNull(message = "产品类型不能为空")
    @ValidEnum(enumClass = ProductTypeEnum.class, message = "可选值：1-T卡、2-U盘、3-SSD")
    private Integer productType;

    /**
     * 主控类型
     */
    @Schema(description = "主控类型。长度不超过50")
    @NotBlank(message = "主控类型不能为空")
    @Length(max = 50)
    private String controllerType;

    /**
     * 接口类型
     */
    @Schema(description = "接口类型。长度不超过50")
    @NotBlank(message = "接口类型不能为空")
    @Length(max = 50)
    private String interfaceType;

    /**
     * 容量(MB)
     */
    @Schema(description = "容量(MB)。")
    @NotNull(message = "容量(MB)不能为空")
    private Integer capacity;

    /**
     * 容量模式
     */
    @Schema(description = "容量模式。长度不超过50")
    @NotBlank(message = "容量模式不能为空")
    @Length(max = 50)
    private String capacityMode;

    /**
     * 速度标记
     */
    @Schema(description = "速度标记。长度不超过50")
    @NotBlank(message = "速度标记不能为空")
    @Length(max = 50)
    private String speedType;

    /**
     * 量产模式
     */
    @Schema(description = "量产模式。长度不超过50")
    @NotBlank(message = "量产模式不能为空")
    @Length(max = 50)
    private String productionMode;

    /**
     * 量产版本
     */
    @Schema(description = "量产版本。长度不超过200")
    @NotBlank(message = "量产版本不能为空")
    @Length(max = 200)
    private String productionVersion;

    /**
     * 量产结果
     */
    @Schema(description = "量产结果。可选值：0-失败、1-成功")
    @NotNull(message = "量产结果不能为空")
    @ValidEnum(enumClass = ProductionResultEnum.class, message = "可选值：0-失败、1-成功")
    private Integer productionResult;

    /**
     * 错误码
     */
    @Schema(description = "错误码。")
    @NotNull(message = "错误码不能为空")
    private Integer errorCode;

    /**
     * 操作系统
     */
    @Schema(description = "操作系统。长度不超过50")
    @NotBlank(message = "操作系统不能为空")
    @Length(max = 50)
    private String osPlatform;

}