package com.dml.cloudtestserver.controller.handler;

import com.dml.cloudtestserver.dao.BatchDao;
import com.dml.cloudtestserver.dao.SampleDao;
import com.dml.cloudtestserver.dao.TableDao;
import com.dml.cloudtestserver.model.batch.BatchRes;
import com.dml.cloudtestserver.model.sample.PerHourSampleCountDto;
import com.dml.cloudtestserver.model.sample.PerHourSuccessRateDto;
import com.dml.cloudtestserver.util.constants.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 当天每小时样片量产数据（数量、成功率）
 */
@Slf4j
@Service
public class TodayHourlyProductionDataHandler extends BaseWebSocketHandler {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private BatchDao batchDao;

    @Autowired
    private SampleDao sampleDao;

    @Autowired
    private TableDao tableDao;


    /**
     * 遍历当天批次UID列表，获取每个批次的每小时的样片数、样片成功率
     */
    @Override
    public Map<String, Object> getData() {
        // 获取当天批次UID列表
        Set<Long> todayBatchUid = getTodayBatchUidList();

        // 所有批次的每小时量产结果
        List<Map<String, Object>> dataList = new ArrayList<>();

        // 获取每个批次的样片数、样片成功率
        for (Long batchUid : todayBatchUid) {
            if (!batchDao.existsUid(batchUid)) {
                continue;
            }

            BatchRes batchRes = batchDao.selectBatch(batchUid);
            if (batchRes == null) {
                continue;
            }

            // 获取批次ID
            String batchId = batchRes.getBatchId();
            String tbSampleName = Constants.TB_SAMPLE_NAME_PREFIX + batchRes.getUid();

            if (tableDao.existTable(tbSampleName) == 0) {
                continue;
            }
            Map<String, Object> map = new ConcurrentHashMap<>();

            map.put("batchUid", String.valueOf(batchUid));
            map.put("batchId", batchId);

            // 每小时样片数
            List<PerHourSampleCountDto> todayHourlySampleNum = Collections.emptyList();
            try {
                todayHourlySampleNum = sampleDao.countHourlySampleNum(tbSampleName);
                todayHourlySampleNum = processHourlySampleNum(todayHourlySampleNum);
            } catch (Exception e) {
                log.error("获取每小时样片数失败：{}", e.getMessage());
            }

            map.put("todayHourlySampleNum", todayHourlySampleNum);

            // 每小时量产成功率
            List<PerHourSuccessRateDto> todayHourlySampleSuccessRate = Collections.emptyList();
            try {
                todayHourlySampleSuccessRate = sampleDao.countHourlySampleSuccessRate(tbSampleName);
                todayHourlySampleSuccessRate = processHourlySampleSuccessRate(todayHourlySampleSuccessRate);
            } catch (Exception e) {
                log.error("获取每小时量产成功率失败：{}", e.getMessage());
            }

            map.put("todayHourlySampleSuccessRate", todayHourlySampleSuccessRate);

            dataList.add(map);
        }

        Map<String, Object> data = new ConcurrentHashMap<>();
//        data.put("todayBatchNum", todayBatchUid.size());
        data.put("dataList", dataList);

        return data;
    }


    /**
     * 处理每小时样本数列表，确保包含1到24小时的数据，缺失的小时count值为0。
     *
     * @param hourlySampleNums 输入的每小时样本数列表
     * @return 处理后的每小时样本数列表
     */
    public static List<PerHourSampleCountDto> processHourlySampleNum(List<PerHourSampleCountDto> hourlySampleNums) {
        // 1. 创建一个包含0到23小时的列表
        Set<String> allHours = new HashSet<>();
        for (int i = 0; i <= 23; i++) {
            allHours.add(String.valueOf(i));
        }

        // 2. 将原始数据转换为小时到样片数量的映射
        Map<String, Integer> map = hourlySampleNums.stream()
                .collect(Collectors.toMap(PerHourSampleCountDto::getHour, PerHourSampleCountDto::getSampleCount));

        // 3. 填充缺失的小时
        List<PerHourSampleCountDto> result = new ArrayList<>();
        for (String hour : allHours) {
            // 如果当前小时在原始数据中没有出现，则将其 sampleCount 设置为0
            Integer sampleCount = map.getOrDefault(hour, 0);
            result.add(new PerHourSampleCountDto(hour, sampleCount));
        }

        // 4. 确保按小时升序排列
        result.sort(Comparator.comparingInt(dto -> Integer.parseInt(dto.getHour())));

        return result;
    }


    /**
     * 处理每小时样本成功率列表，确保包含1到24小时的数据，缺失的小时successRate值为0.0。
     *
     * @param hourlySampleSuccessRates 输入的每小时样本成功率列表
     * @return 处理后的每小时样本成功率列表
     */
    public static List<PerHourSuccessRateDto> processHourlySampleSuccessRate(List<PerHourSuccessRateDto> hourlySampleSuccessRates) {
        // 1. 创建一个包含0到23小时的列表
        Set<String> allHours = new HashSet<>();
        for (int i = 0; i <= 23; i++) {
            allHours.add(String.valueOf(i));
        }

        // 2. 将原始数据转换为小时到样片数量的映射
        Map<String, Double> map = hourlySampleSuccessRates.stream()
                .collect(Collectors.toMap(PerHourSuccessRateDto::getHour, PerHourSuccessRateDto::getSuccessRate));

        // 3. 填充缺失的小时
        List<PerHourSuccessRateDto> result = new ArrayList<>();
        for (String hour : allHours) {
            // 如果当前小时在原始数据中没有出现，则将其 sampleCount 设置为0
            Double successRate = map.getOrDefault(hour, 0.0000);
            result.add(new PerHourSuccessRateDto(hour, successRate));
        }

        // 4. 确保按小时升序排列
        result.sort(Comparator.comparingInt(dto -> Integer.parseInt(dto.getHour())));

        return result;
    }


    /**
     * 获取当天批次UID列表
     *
     * @return 当天批次UID列表
     */
    public Set<Long> getTodayBatchUidList() {
        Set<String> stringSet = stringRedisTemplate.opsForSet().members(Constants.REDIS_KEY_TODAY_BATCH_UID_LIST);
        if (stringSet != null) {
            return stringSet.stream()
                    .map(s -> {
                        try {
                            return Long.parseLong(s);
                        } catch (NumberFormatException e) {
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
        } else {
            System.out.println("没有获取到有效的集合数据");
            return new HashSet<>();
        }
    }


}
