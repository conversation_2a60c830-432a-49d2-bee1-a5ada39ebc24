package com.dml.cloudtestserver.controller;


import cn.dev33.satoken.annotation.SaCheckPermission;
import com.dml.cloudtestserver.model.user_power.UserPowerReq;
import com.dml.cloudtestserver.service.UserPowerService;
import com.dml.cloudtestserver.util.response.ResponseResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Tag(name = "用户权力管理")
@RestController
@Validated // 使 Controller 支持参数级别校验，支持列表里的类字段校验
@RequestMapping("/api/userPower")
public class UserPowerController {

    @Autowired
    private UserPowerService userPowerService;


    @Operation(summary = "批量更新用户权力")
    @PutMapping("/{userUid}")
    @SaCheckPermission("user-power-update")
    public ResponseResult<?> updateUserPower(
            @PathVariable
            Long userUid,

            @Schema(implementation = UserPowerReq.class)
            @Valid
            @RequestBody
            List<UserPowerReq> userPowerReqList) {
        return userPowerService.updateUserPower(userUid, userPowerReqList);
    }


    @Operation(summary = "查询用户所有权力")
    @GetMapping("/{userUid}")
    @SaCheckPermission("user-power-query")
    public ResponseResult<?> queryUserPower(
            @PathVariable
            Long userUid
//            @Parameter(description = "权力范围") @RequestParam(value = "scope", required = false) Integer scope
    ) {
        return userPowerService.queryUserAllPower(userUid, null);
    }


}
