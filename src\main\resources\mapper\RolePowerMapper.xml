<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dml.cloudtestserver.dao.RolePowerDao">


    <!-- 结果映射，将数据库字段映射到 RolePowerEntity 对象的属性 -->
    <resultMap id="BaseResultMap" type="com.dml.cloudtestserver.model.role_power.RolePowerEntity">
        <result column="uid" property="uid"/>
        <result column="role_uid" property="roleUid"/>
        <result column="scope" property="scope"/>
        <result column="power_uid" property="powerUid"/>
        <result column="granted_time" property="grantedTime"/>
    </resultMap>

    <insert id="insertRolePower" parameterType="com.dml.cloudtestserver.model.role_power.RolePowerReq">
        INSERT INTO tb_role_power (uid, role_uid, scope, power_uid)
        VALUES (#{uid}, #{roleUid}, #{scope}, #{powerUid})
    </insert>

    <delete id="deleteRolePower" parameterType="Long">
        DELETE
        FROM tb_role_power
        WHERE uid = #{uid}
    </delete>


    <select id="selectRolePower" resultMap="BaseResultMap">
        SELECT uid, role_uid, scope, power_uid, granted_time
        FROM tb_role_power
        WHERE uid = #{uid}
    </select>


    <select id="selectRolePowerList" resultMap="BaseResultMap">
        SELECT uid, role_uid, scope, power_uid, granted_time
        FROM tb_role_power
        <where>
            <if test="scope != null">
                AND scope = #{scope}
            </if>
        </where>
    </select>


    <select id="existsUid" resultType="boolean">
        SELECT EXISTS (SELECT 1
                       FROM tb_role_power
                       WHERE uid = #{uid})
    </select>

    <select id="existsUidAndPowerId" resultType="boolean">
        SELECT EXISTS (SELECT 1
                       FROM tb_role_power
                       WHERE role_uid = #{roleUid}
                         AND power_uid = #{powerUid})
    </select>


    <select id="selectAllPermissionUid" resultType="Long">
        SELECT power_uid
        FROM tb_role_power
        WHERE role_uid = #{roleUid}
          AND scope = #{scope}
    </select>


    <select id="selectAllBatchUid" resultType="Long">
        SELECT power_uid
        FROM tb_role_power
        WHERE role_uid = #{roleUid}
          AND scope = #{scope}
    </select>

</mapper>
