package com.dml.cloudtestserver.service;

import cn.hutool.core.util.IdUtil;
import com.dml.cloudtestserver.dao.BatchDao;
import com.dml.cloudtestserver.dao.PermissionDao;
import com.dml.cloudtestserver.dao.RoleDao;
import com.dml.cloudtestserver.dao.RolePowerDao;
import com.dml.cloudtestserver.model.role_power.RolePowerDto;
import com.dml.cloudtestserver.model.role_power.RolePowerEntity;
import com.dml.cloudtestserver.model.role_power.RolePowerReq;
import com.dml.cloudtestserver.util.response.ResponseEnum;
import com.dml.cloudtestserver.util.response.ResponseResult;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j
@Service
public class RolePowerService {

    @Autowired
    private RolePowerDao rolePowerDao;

    @Autowired
    private RoleDao roleDao;

    @Autowired
    private PermissionDao permissionDao;

    @Autowired
    private BatchDao batchDao;


    /**
     * 添加角色权力
     */
    public ResponseResult<?> addRolePower(RolePowerReq rolePowerReq) {
        // 数据
        Long roleUid = rolePowerReq.getRoleUid();
        Integer scope = rolePowerReq.getScope();
        Long powerUid = rolePowerReq.getRoleUid();

        // 判断是否存在
        if (!roleDao.existsUid(roleUid)) {
            return ResponseResult.error(ResponseEnum.DATA_NOT_EXISTS, "角色不存在");
        }

        if (scope.equals(1)) {
            if (!permissionDao.existsUid(powerUid)) {
                return ResponseResult.error(ResponseEnum.DATA_NOT_EXISTS, "权限不存在");
            }
        } else if (scope.equals(2)) {
            if (!batchDao.existsUid(powerUid)) {
                return ResponseResult.error(ResponseEnum.DATA_NOT_EXISTS, "批次不存在");
            }
        }

        // 判断是否已添加
        if (rolePowerDao.existsUidAndPowerId(roleUid, powerUid)) {
            return ResponseResult.error(ResponseEnum.DATA_ALREADY_EXISTS, "权力已添加");
        }

        // 数据对象
        Long uid = IdUtil.getSnowflakeNextId();
        RolePowerDto rolePowerDto = new RolePowerDto(uid, roleUid, scope, powerUid);

        // 插入
        int affectedRows = rolePowerDao.insertRolePower(rolePowerDto);
        if (affectedRows == 0) {
            return ResponseResult.error(ResponseEnum.ADD_ERROR);
        }

        // 查询数据
        RolePowerEntity rolePowerEntity = rolePowerDao.selectRolePower(uid);

        // 返回结果
        return ResponseResult.success(ResponseEnum.ADD_SUCCESS, rolePowerEntity);
    }


    /**
     * 删除角色权力
     */
    public ResponseResult<?> deleteRolePower(Long uid) {
        // 检查角色权力是否存在
        if (!rolePowerDao.existsUid(uid)) {
            return ResponseResult.error(ResponseEnum.DATA_NOT_EXISTS, "角色权力不存在");
        }

        // 删除角色权力
        int affectedRows = rolePowerDao.deleteRolePower(uid);
        if (affectedRows == 0) {
            return ResponseResult.error(ResponseEnum.DELETE_ERROR);
        }

        // 返回结果
        return ResponseResult.success(ResponseEnum.DELETE_SUCCESS);
    }


    /**
     * 批量查询角色权力
     */
    public ResponseResult<?> queryAllRolePower(int pageNum, int pageSize, Integer scope) {
        // 开启分页
        PageHelper.startPage(pageNum, pageSize);

        // 查询数据
        List<RolePowerEntity> rolePowerEntityList = rolePowerDao.selectRolePowerList(scope);

        // 返回结果
        return ResponseResult.success(ResponseEnum.QUERY_SUCCESS, rolePowerEntityList);
    }

}
