package com.dml.cloudtestserver.util.constants;


/**
 * 常量类，用于定义项目中的常量
 */
public final class Constants {

    private Constants() {
        throw new AssertionError("常量类不允许实例化");
    }

    /**
     * 样片表名称前缀
     */
    public static final String TB_SAMPLE_NAME_PREFIX = "tb_sample_";

    /**
     * 默认用户密码
     */
    public static final String DEFAULT_USER_PASSWORD = "123456";


    /**
     * 默认分页配置
     */
    public static final String DEFAULT_PAGE_NUM = "1";  // 默认起始页码
    public static final String DEFAULT_PAGE_SIZE = "10"; // 默认每页数量


    /**
     * Redis键
     */
    public static final String REDIS_KEY_TODAY_BATCH_UID_LIST = "todayBatchUidList"; // 当天批次UID列表的键
    public static final String REDIS_KEY_USER_PWD_ERR_NUM = "userPwdErrNum"; // 用户密码错误次数

    /**
     * 正则表达式
     * 1、下划线：不能开头或结尾、不能仅有下划线
     */

    // 中文、字母、数字、下划线
    public static final String REGEX_CHINESE_LETTER_NUMBER_UNDERLINE = "^(?!_)(?!.*?_$)[\\u4e00-\\u9fa5\\w]+$";
    public static final String REGEX_CHINESE_LETTER_NUMBER_UNDERLINE_MSG = "由中文、字母、数字、下划线组成（下划线不能开头或结尾或仅有下划线）";

    // 中文、字符、数字、下划线、井号#、圆括号()
    public static final String REGEX_LETTER_NUMBER_UNDERLINE_WELL_PARENTHESES = "^[\\u4e00-\\u9fa5a-zA-Z0-9_#()]+$";
    public static final String REGEX_LETTER_NUMBER_UNDERLINE_WELL_PARENTHESES_MSG = "由中文、字符、数字、下划线、井号#、圆括号()组成";

    // 字母、数字、下划线
    public static final String REGEX_LETTER_NUMBER_UNDERLINE = "[a-zA-Z0-9_]+$";
    public static final String REGEX_LETTER_NUMBER_UNDERLINE_MSG = "由字母、数字、下划线组成";

    // 字母、数字、短横线
    public static final String REGEX_LETTER_NUMBER_DASH = "^(?!-)(?!.*?-$)[a-zA-Z0-9-]+$";
    public static final String REGEX_LETTER_NUMBER_DASH_MSG = "由字母、数字、短横线组成（短横线不能开头或结尾或仅有短横线）";

    // 中文、字母、数字
    public static final String REGEX_CHINESE_LETTER_NUMBER = "^[\\u4e00-\\u9fa5a-zA-Z0-9]+$";
    public static final String REGEX_CHINESE_LETTER_NUMBER_MSG = "由中文、字母、数字组成";

    // 中文、字母
    public static final String REGEX_CHINESE_LETTER = "^[\\u4e00-\\u9fa5a-zA-Z]+$";
    public static final String REGEX_CHINESE_LETTER_MSG = "由中文、字母组成";

    // 字母
    public static final String REGEX_LETTER = "^[A-Za-z]+$";
    public static final String REGEX_LETTER_MSG = "由字母组成";

    // 文件/目录名规则，不包含非法字符  < > : " / \ | ? *
    public static final String REGEX_FILE_DIR = "^[^<>\":/\\\\|?*]*$";
    public static final String REGEX_FILE_DIR_MSG = "文件目录非法字符 < > : \" / \\ | ? *";

    // 路由路径
    public static final String REGEX_ROUTE_PATH = "^(\\/|\\/([A-Za-z0-9]+|[A-Za-z0-9]+[A-Za-z0-9_-]*[A-Za-z0-9]+)(\\/([A-Za-z0-9]+|[A-Za-z0-9]+[A-Za-z0-9_-]*[A-Za-z0-9]+))*)?$";
    public static final String REGEX_ROUTE_PATH_MSG = "非法的路由路径格式";

}