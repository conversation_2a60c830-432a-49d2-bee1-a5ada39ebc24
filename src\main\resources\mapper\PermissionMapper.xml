<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dml.cloudtestserver.dao.PermissionDao">


    <!-- 结果映射，将数据库字段映射到 PermissionEntity 对象的属性 -->
    <resultMap id="BaseResultMap" type="com.dml.cloudtestserver.model.permission.PermissionEntity">
        <result column="uid" property="uid"/>
        <result column="scope" property="scope"/>
        <result column="name" property="name"/>
        <result column="parent_uid" property="parentUid"/>
        <result column="code" property="code"/>
        <result column="sort_order" property="sortOrder"/>
    </resultMap>

    <resultMap id="FuncEntityBaseResultMap" type="com.dml.cloudtestserver.model.permission.FuncEntity">
        <result column="uid" property="uid"/>
        <result column="name" property="name"/>
        <result column="code" property="code"/>
    </resultMap>


    <select id="existsUid" resultType="boolean">
        SELECT EXISTS (SELECT 1
                       FROM tb_permission
                       WHERE uid = #{uid})
    </select>

    <select id="existsParentUid" resultType="boolean">
        SELECT EXISTS (SELECT 1
                       FROM tb_permission
                       WHERE parent_uid = #{parentUid})
    </select>

    <select id="existsName" resultType="boolean">
        SELECT EXISTS (SELECT 1
                       FROM tb_permission
                       WHERE name = #{name})
    </select>


    <select id="existsCode" resultType="boolean">
        SELECT EXISTS (SELECT 1
                       FROM tb_permission
                       WHERE code = #{code})
    </select>

    <insert id="insertPermission" parameterType="com.dml.cloudtestserver.model.permission.PermissionEntity">
        INSERT INTO tb_permission (uid, scope, name, parent_uid, code, sort_order)
        VALUES (#{uid}, #{scope}, #{name}, #{parentUid}, #{code}, #{sortOrder})
    </insert>

    <delete id="deletePermission">
        DELETE
        FROM tb_permission
        WHERE uid = #{uid}
    </delete>

    <update id="updatePermission" parameterType="com.dml.cloudtestserver.model.permission.PermissionEntity">
        UPDATE tb_permission
        SET scope      = #{scope},
            name       = #{name},
            parent_uid = #{parentUid},
            code       = #{code},
            sort_order = #{sortOrder}
        WHERE uid = #{uid}
    </update>


    <select id="selectPermission" resultMap="BaseResultMap">
        SELECT uid,
               scope,
               name,
               parent_uid,
               code,
               sort_order
        FROM tb_permission
        WHERE uid = #{uid}
    </select>


    <select id="selectPermissionByFuncCode" resultMap="BaseResultMap">
        SELECT uid,
               scope,
               name,
               parent_uid,
               code,
               sort_order
        FROM tb_permission
        WHERE code = #{funcCode}
    </select>


    <select id="selectAllPermission" resultMap="BaseResultMap">
        SELECT uid,
        scope,
        name,
        parent_uid,
        code,
        sort_order
        FROM tb_permission
        <where>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
        </where>
    </select>


    <select id="selectFuncList" resultMap="FuncEntityBaseResultMap">
        SELECT uid, name, code
        FROM tb_permission
        <where>
            AND scope = 3
        </where>
    </select>


    <select id="selectAllSortOrderByParentUid" resultType="int">
        SELECT sort_order
        FROM tb_permission
        WHERE parent_uid = #{parentUid}
    </select>


</mapper>
