package com.dml.cloudtestserver.service;

import cn.dev33.satoken.stp.SaLoginConfig;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.crypto.SecureUtil;
import com.dml.cloudtestserver.dao.BatchDao;
import com.dml.cloudtestserver.dao.PermissionDao;
import com.dml.cloudtestserver.dao.UserDao;
import com.dml.cloudtestserver.dao.UserPowerDao;
import com.dml.cloudtestserver.model.auth.FtpInfoRes;
import com.dml.cloudtestserver.model.auth.LoginReq;
import com.dml.cloudtestserver.model.batch.BatchInfoRes;
import com.dml.cloudtestserver.model.batch.BatchRes;
import com.dml.cloudtestserver.model.permission.FuncEntity;
import com.dml.cloudtestserver.model.permission.PermissionEntity;
import com.dml.cloudtestserver.model.user.UserEntity;
import com.dml.cloudtestserver.util.constants.PermissionScopeEnum;
import com.dml.cloudtestserver.util.constants.UserCategoryEnum;
import com.dml.cloudtestserver.util.constants.UserStatusEnum;
import com.dml.cloudtestserver.util.properties.FtpProperties;
import com.dml.cloudtestserver.util.response.ResponseEnum;
import com.dml.cloudtestserver.util.response.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class AuthService {

    @Autowired
    private UserDao userDao;

    @Autowired
    private UserPowerDao userPowerDao;

    @Autowired
    private PermissionDao permissionDao;

    @Autowired
    private BatchDao batchDao;

    @Autowired
    private FtpProperties ftpProperties;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private HashOperations<String, String, Object> hashOperations;

    /**
     * 登录
     */
    public ResponseResult<?> login(LoginReq loginReq) {
        String account = loginReq.getAccount();
        String password = loginReq.getPassword();
        String passwordMd5 = SecureUtil.md5(password);

        /*
         * 判断
         */

        // 判断账号是否已存在
        if (!userDao.existsAccount(account)) {
            return ResponseResult.error(ResponseEnum.DATA_NOT_EXISTS, "账号不存在");
        }

        // 判断密码是否正确
        UserEntity userEntity = userDao.selectUserByAccount(account);
        Long userUid = userEntity.getUid();
        if (!passwordMd5.equals(userEntity.getPassword())) {
            return ResponseResult.error(ResponseEnum.PASSWORD_ERROR);
        }

//        if (!passwordMd5.equals(userEntity.getPassword())) {
//            // 当天密码错误5次，禁用账号
//            Integer errorCount = (Integer) hashOperations.get(Constants.REDIS_KEY_USER_PWD_ERR_NUM,
//                    String.valueOf(userUid));
//            if (errorCount == null) {
//                errorCount = 1;
//            } else {
//                errorCount++;
//            }
//
//            if (errorCount > 5) {
//                UserUpdateDto userUpdateDto = new UserUpdateDto(userUid, account, UserStatusEnum.DISABLE.getValue());
//                int affectedRows = userDao.updateUser(userUpdateDto);
//                if (affectedRows == 0) {
//                    return ResponseResult.error(ResponseEnum.ACCOUNT_DISABLE);
//                }
//                return ResponseResult.error(ResponseEnum.ACCOUNT_DISABLE, "密码错误当天达到5次，账号已禁用，请联系管理员启用");
//            } else {
//
//                if (Boolean.FALSE.equals(stringRedisTemplate.hasKey(Constants.REDIS_KEY_USER_PWD_ERR_NUM))) {
//                    hashOperations.put(Constants.REDIS_KEY_USER_PWD_ERR_NUM, String.valueOf(userUid), errorCount);
//
//                    // 判断键是否存在，不存在则设置有效期为次日凌晨
//                    LocalDateTime endOfDay = LocalDate.now().atTime(23, 59, 59);
//                    Date expirationDate = Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
//                    stringRedisTemplate.expireAt(Constants.REDIS_KEY_USER_PWD_ERR_NUM, expirationDate);
//                } else {
//                    hashOperations.put(Constants.REDIS_KEY_USER_PWD_ERR_NUM, String.valueOf(userUid), errorCount);
//                }
//            }
//
//            return ResponseResult.error(ResponseEnum.PASSWORD_ERROR);
//        }

        // 判断是否禁用
        if (userEntity.getStatus() == UserStatusEnum.DISABLE.getValue()) {
            return ResponseResult.error(ResponseEnum.ACCOUNT_DISABLE);
        }

        /*
         * 登录
         */

        String loginUserId = String.valueOf(userUid);
        StpUtil.login(loginUserId, SaLoginConfig
                .setExtra("category", userEntity.getCategory())
                .setExtra("createUserUid", userEntity.getCreateUserUid())
                .setExtra("permission", getPermissionPower(userUid))
        );

        String token = StpUtil.getTokenValue();
        Map<String, Object> data = new HashMap<>();
        data.put("account", userEntity.getAccount());
        data.put("token", token);

        return ResponseResult.success("登录成功", data);
    }

    /**
     * 退出登录
     */
    public ResponseResult<?> logout() {
        StpUtil.logout();
        return ResponseResult.success("退出登录成功");
    }

    /**
     * 获取FTP信息
     */
    public ResponseResult<?> getFtpInfo(String key) {
        if (!key.equals(ftpProperties.getKey())) {
            return ResponseResult.error(ResponseEnum.KEY_ERROR);
        }

        FtpInfoRes ftpInfoRes = new FtpInfoRes(
                ftpProperties.getHost(), ftpProperties.getPort(), ftpProperties.getAccount(),
                ftpProperties.getPassword());
        return ResponseResult.success(ResponseEnum.QUERY_SUCCESS, ftpInfoRes);
    }

    /**
     * 获取登录用户的功能列表
     */
    public ResponseResult<?> getFuncList() {
        // 获取用户UID
        Long loginUserUid = StpUtil.getLoginIdAsLong();
        UserEntity loginUserEntity = userDao.selectUser(loginUserUid);

        // 功能列表
        List<FuncEntity> funcEntityList = new ArrayList<>();

        if (loginUserEntity.getCategory().equals(UserCategoryEnum.SUPER_ADMIN.getValue())) {
            funcEntityList = permissionDao.selectFuncList();
        } else {
            List<Long> permissionUidList = userPowerDao.selectAllPermissionUid(loginUserUid);
            for (Long permissionUid : permissionUidList) {
                PermissionEntity permissionEntity = permissionDao.selectPermission(permissionUid);
                if (permissionEntity != null && permissionEntity.getScope() == PermissionScopeEnum.FUNC.getValue()) {
                    FuncEntity funcEntity = new FuncEntity(permissionEntity.getUid(), permissionEntity.getName(),
                            permissionEntity.getCode());
                    funcEntityList.add(funcEntity);
                }
            }
        }

        return ResponseResult.success(ResponseEnum.QUERY_SUCCESS, funcEntityList);
    }

    /**
     * 获取登录用户的权限列表z
     */
    public ResponseResult<?> getPermissionList() {
        Long userUid = StpUtil.getLoginIdAsLong();
        List<PermissionEntity> permissionList = getPermissionPower(userUid);
        return ResponseResult.success(ResponseEnum.QUERY_SUCCESS, permissionList);
    }

    /**
     * 获取登录用户的批次列表
     */
    public ResponseResult<?> getBatchList() {
        Long userUid = StpUtil.getLoginIdAsLong();
        List<BatchRes> batchList = getBatchPower(userUid);
        List<BatchInfoRes> batchInfoResList = new ArrayList<>();
        for (BatchRes batchRes : batchList) {
            batchInfoResList.add(
                    new BatchInfoRes(
                            batchRes.getUid(),
                            batchRes.getBatchId(),
                            batchRes.getPreProductionSampleNum(),
                            batchRes.getStatus(),
                            batchRes.getCreatedTime()
                    )
            );
        }
        return ResponseResult.success(ResponseEnum.QUERY_SUCCESS, batchInfoResList);
    }

    public List<PermissionEntity> getPermissionPower(Long userUid) {
        UserEntity userEntity = userDao.selectUser(userUid);

        // 权限信息
        List<PermissionEntity> permissionList = new ArrayList<>();

        if (userEntity.getCategory().equals(UserCategoryEnum.SUPER_ADMIN.getValue())) {
            permissionList = permissionDao.selectAllPermission(null);
        } else {
            List<Long> permissionUidList = userPowerDao.selectAllPermissionUid(userUid);

            for (Long permissionUid : permissionUidList) {
                PermissionEntity permissionEntity = permissionDao.selectPermission(permissionUid);
                if (permissionEntity != null) {
                    permissionList.add(permissionEntity);
                }
            }
        }

        return permissionList;
    }

    public List<BatchRes> getBatchPower(Long userUid) {
        UserEntity userEntity = userDao.selectUser(userUid);
        List<BatchRes> batchList = new ArrayList<>();

        if (userEntity.getCategory().equals(UserCategoryEnum.SUPER_ADMIN.getValue())) {
            batchList = batchDao.selectAllBatch();
        } else {
            List<Long> batchUidList = userPowerDao.selectAllBatchUid(userUid);
            for (Long batchUid : batchUidList) {
                BatchRes batchRes = batchDao.selectBatch(batchUid);
                if (batchRes != null) {
                    batchList.add(batchRes);
                }
            }
        }

        return batchList;
    }

}
