package com.dml.cloudtestserver.controller;

import com.dml.cloudtestserver.service.AssignablePermissionService;
import com.dml.cloudtestserver.util.response.ResponseResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "可分配权限管理")
@RestController
@RequestMapping("/api/assignablePermission")
public class AssignablePermissionController {

    @Autowired
    private AssignablePermissionService AssignablePermissionService;

//    @Operation(summary = "批量更新可分配权限")
//    @PutMapping("")
//    @SaCheckPermission("assignable-permission-update")
//    public ResponseResult<?> updateAssignablePermissionList(
//            @Valid @RequestBody AssignablePermissionReq assignablePermissionReq) {
//        return AssignablePermissionService.updateAssignablePermission(assignablePermissionReq);
//    }

    @Operation(summary = "批量查询可分配权限")
    @GetMapping("")
    public ResponseResult<?> queryAllAssignablePermission(
            @Parameter(description = "用户类别") @RequestParam(value = "userCategory", required = false) Integer userCategory) {
        return AssignablePermissionService.queryAllAssignablePermission(userCategory);
    }

}
