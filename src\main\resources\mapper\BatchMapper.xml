<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dml.cloudtestserver.dao.BatchDao">


    <!-- 结果映射，将数据库字段映射到 BatchEntity 对象的属性 -->
    <resultMap id="BaseResultMap" type="com.dml.cloudtestserver.model.batch.BatchRes">
        <result column="uid" property="uid"/>
        <result column="batch_id" property="batchId"/>
        <result column="pre_production_sample_num" property="preProductionSampleNum"/>
        <result column="status" property="status"/>
        <result column="created_time" property="createdTime"/>
    </resultMap>


    <select id="existsUid" resultType="boolean">
        SELECT EXISTS (SELECT 1
                       FROM tb_batch
                       WHERE uid = #{uid})
    </select>

    <select id="existsBatchId" resultType="boolean">
        SELECT EXISTS (SELECT 1
                       FROM tb_batch
                       WHERE batch_id = #{batchId})
    </select>

    <select id="countBatchNum" resultType="int">
        SELECT COUNT(*)
        FROM tb_batch
    </select>


    <insert id="insertBatch" parameterType="com.dml.cloudtestserver.model.batch.BatchDto">
        INSERT INTO tb_batch (uid, batch_id, tb_sample_name, pre_production_sample_num, status)
        VALUES (#{uid}, #{batchId}, #{tbSampleName}, #{preProductionSampleNum}, #{status})
    </insert>


    <delete id="deleteBatchByUid" parameterType="Long">
        DELETE
        FROM tb_batch
        WHERE uid = #{uid}
    </delete>


    <update id="updateBatch" parameterType="com.dml.cloudtestserver.model.batch.BatchDto">
        UPDATE tb_batch
        SET batch_id                  = #{batchId},
            tb_sample_name            = #{tbSampleName},
            pre_production_sample_num = #{preProductionSampleNum},
            status                    = #{status}
        WHERE uid = #{uid}
    </update>


    <select id="selectBatch" parameterType="Long" resultMap="BaseResultMap">
        SELECT uid, batch_id, pre_production_sample_num, status, created_time
        FROM tb_batch
        WHERE uid = #{uid}
    </select>


    <select id="selectBatchList" resultMap="BaseResultMap">
        SELECT uid, batch_id, pre_production_sample_num, status, created_time
        FROM tb_batch
        <where>
            <if test="batchId != null and batchId != ''">
                AND batch_id LIKE CONCAT('%', #{batchId}, '%')
            </if>
            <if test="true">
                <if test="status != null and status.size() > 0">
                    AND (status IN
                    <foreach item="item" index="index" collection="status" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    )
                </if>
                <if test="status == null or status.size() == 0">
                    AND (status IS NULL OR status = '')
                </if>
            </if>
            <if test="startCreatedTime != null">
                AND created_time &gt;= #{startCreatedTime}
            </if>
            <if test="endCreatedTime != null">
                AND created_time &lt;= #{endCreatedTime}
            </if>
        </where>
    </select>


    <select id="selectAllBatch" resultMap="BaseResultMap">
        SELECT uid, batch_id, pre_production_sample_num, status, created_time
        FROM tb_batch
    </select>


</mapper>