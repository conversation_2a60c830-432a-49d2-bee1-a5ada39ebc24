package com.dml.cloudtestserver.dao;

import com.dml.cloudtestserver.model.role.RoleEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface RoleDao {

    // UID是否存在
    boolean existsUid(Long uid);

    // 角色名称是否存在
    boolean existsName(String name);

    // 插入角色
    int insertRole(RoleEntity roleEntity);

    // 删除角色
    int deleteRole(Long uid);

    // 更新角色
    int updateRole(RoleEntity roleEntity);

    // 查询角色
    RoleEntity selectRole(Long uid);

    // 查询角色列表
    List<RoleEntity> selectRoleList(@Param("name") String name);

}
