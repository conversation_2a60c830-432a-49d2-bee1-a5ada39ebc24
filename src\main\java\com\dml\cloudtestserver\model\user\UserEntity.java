package com.dml.cloudtestserver.model.user;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;


/**
 * 用户表实体
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserEntity implements Serializable {

    private static final long serialVersionUID = 3437676604789930688L;

    /**
     * UID
     */
    private Long uid;


    /**
     * 创建者UID
     */
    private Long createUserUid;


    /**
     * 账号
     */
    private String account;


    /**
     * 密码
     */
    private String password;


    /**
     * 状态
     */
    private Integer status;


    /**
     * 类别
     */
    private Integer category;


    /**
     * 创建时间
     */
    private Date createdTime;


    /**
     * 更新时间
     */
    private Date updatedTime;
}
