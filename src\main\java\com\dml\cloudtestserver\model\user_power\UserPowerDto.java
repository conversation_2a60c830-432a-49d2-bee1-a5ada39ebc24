package com.dml.cloudtestserver.model.user_power;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 用户-权力表实体
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserPowerDto implements Serializable {

    private static final long serialVersionUID = 6370443483743103147L;

    /**
     * UID
     */
    private Long uid;


    /**
     * 用户UID
     */
    private Long userUid;


    /**
     * 权力范围
     */
    private Integer scope;


    /**
     * 权力UID
     */
    private Long powerUid;
}
