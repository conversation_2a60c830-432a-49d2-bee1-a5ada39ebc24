package com.dml.cloudtestserver.model.user;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserInsertDto implements Serializable {

    private static final long serialVersionUID = 4366891078541615299L;

    /**
     * UID
     */
    private Long uid;

    /**
     * 创建者UID
     */
    private Long createUserUid;

    /**
     * 账号
     */
    private String account;

    /**
     * 密码
     */
    private String password;

    /**
     * 状态
     */
    private Integer status;


    /**
     * 类别
     */
    private Integer category;


}