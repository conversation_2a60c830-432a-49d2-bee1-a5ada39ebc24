package com.dml.cloudtestserver.dao;

import com.dml.cloudtestserver.model.batch.BatchDto;
import com.dml.cloudtestserver.model.batch.BatchRes;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Mapper
@Repository
public interface BatchDao {

    // UID是否存在
    boolean existsUid(Long uid);

    // 批次ID是否存在
    boolean existsBatchId(String batchId);

    // 总批次数量
    int countBatchNum();

    // 插入批次
    int insertBatch(BatchDto batchDto);

    // 删除批次
    int deleteBatchByUid(Long uid);

    // 更新批次
    int updateBatch(BatchDto batchDto);

    // 查询批次
    BatchRes selectBatch(Long uid);

    // 查询批次列表
    List<BatchRes> selectBatchList(@Param("batchId") String batchId,
                                   @Param("status") List<Integer> status,
                                   @Param("startCreatedTime") Date startCreatedTime,
                                   @Param("endCreatedTime") Date endCreatedTime);

    // 查询所有批次
    List<BatchRes> selectAllBatch();

}
