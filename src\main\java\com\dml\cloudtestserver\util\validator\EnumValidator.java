package com.dml.cloudtestserver.util.validator;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.lang.reflect.Method;

/**
 * 自定义校验器，用于校验一个字段或参数的值是否在指定的枚举类型中。
 */
public class EnumValidator implements ConstraintValidator<ValidEnum, Object> {
    private Enum<?>[] enumValues;  // 存储指定的枚举值
    private Method getValueMethod;  // 存储枚举类的 getValue 方法

    /**
     * 初始化方法，获取注解中指定的枚举类并检查是否包含 getValue 方法。
     *
     * @param annotation ValidEnum 注解的实例
     */
    @Override
    public void initialize(ValidEnum annotation) {
        Class<?> enumClass = annotation.enumClass();  // 获取注解中指定的枚举类
        this.enumValues = (Enum<?>[]) enumClass.getEnumConstants();  // 获取枚举常量
        try {
            // 尝试获取枚举类中的 getValue 方法
            this.getValueMethod = enumClass.getMethod("getValue");
        } catch (NoSuchMethodException e) {
            // 如果枚举类没有 getValue 方法，抛出异常
            throw new IllegalArgumentException("Enum class must have a 'getValue' method", e);
        }
    }

    /**
     * 校验目标值是否在指定的枚举值中。
     *
     * @param value   被校验的值
     * @param context 校验上下文
     * @return 如果目标值在枚举中返回 true，否则返回 false
     */
    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        if (value == null) {
            return true;  // 如果值为 null，根据需求可以返回 true 或 false
        }
        try {
            // 遍历枚举值，检查目标值是否匹配
            for (Enum<?> enumValue : enumValues) {
                Object enumValueInstance = getValueMethod.invoke(enumValue);  // 调用 getValue 方法获取枚举的值
                if (enumValueInstance.equals(value)) {  // 比较目标值和枚举的值
                    return true;
                }
            }
        } catch (Exception e) {
            // 如果在校验过程中发生异常，抛出运行时异常
            throw new RuntimeException("Error while validating enum values", e);
        }
        return false;  // 如果目标值不匹配任何枚举值，返回 false
    }
}
