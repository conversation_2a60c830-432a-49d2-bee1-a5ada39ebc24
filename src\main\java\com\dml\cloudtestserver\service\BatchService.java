package com.dml.cloudtestserver.service;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.IdUtil;
import com.dml.cloudtestserver.dao.BatchDao;
import com.dml.cloudtestserver.dao.TableDao;
import com.dml.cloudtestserver.dao.UserDao;
import com.dml.cloudtestserver.dao.UserPowerDao;
import com.dml.cloudtestserver.model.batch.BatchDto;
import com.dml.cloudtestserver.model.batch.BatchReq;
import com.dml.cloudtestserver.model.batch.BatchRes;
import com.dml.cloudtestserver.model.user.UserEntity;
import com.dml.cloudtestserver.util.constants.Constants;
import com.dml.cloudtestserver.util.constants.UserCategoryEnum;
import com.dml.cloudtestserver.util.response.ResponseEnum;
import com.dml.cloudtestserver.util.response.ResponseResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


@Slf4j
@Service
public class BatchService {

    @Autowired
    private BatchDao batchDao;

    @Autowired
    private TableDao tableDao;

    @Autowired
    private UserPowerDao userPowerDao;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private UserDao userDao;

    /**
     * 添加批次
     */
    @Transactional
    public ResponseResult<?> addBatch(BatchReq batchReq) {
        // 数据
        String batchId = batchReq.getBatchId();
        Integer status = batchReq.getStatus();

        // 判断新批次号是否已存在
        if (batchDao.existsBatchId(batchId)) {
            return ResponseResult.error(ResponseEnum.DATA_ALREADY_EXISTS, "批次号已存在");
        }

        // 数据对象
        Long uid = IdUtil.getSnowflakeNextId();
        String tbSampleName = Constants.TB_SAMPLE_NAME_PREFIX + uid;
        BatchDto batchDto = new BatchDto(uid, batchId, tbSampleName, batchReq.getPreProductionSampleNum(), status);

        // 插入
        int affectedRows = batchDao.insertBatch(batchDto);
        if (affectedRows == 0) {
            return ResponseResult.error(ResponseEnum.ADD_ERROR);
        }

        // 创建样片表
        tableDao.createSampleTable(tbSampleName);
        tableDao.addSampleTableIndex(tbSampleName);

        // 查询创建的批次数据
        BatchRes batchRes = batchDao.selectBatch(uid);

        // 返回结果
        return ResponseResult.success(ResponseEnum.ADD_SUCCESS, batchRes);

    }


    /**
     * 删除批次
     */
    @Transactional
    public ResponseResult<?> deleteBatch(Long uid) {
        // 检查批次是否存在
        BatchRes batchRes = batchDao.selectBatch(uid);
        if (batchRes == null) {
            return ResponseResult.error(ResponseEnum.DATA_NOT_EXISTS, "批次不存在");
        }

        // 数据
        String tbSampleName = Constants.TB_SAMPLE_NAME_PREFIX + batchRes.getUid();

        // 删除样片表
        if (tableDao.existTable(tbSampleName) > 0) {
            // 检查样片表是否有数据
            int rowCount = tableDao.countRowsInTable(tbSampleName);
            if (rowCount > 0) {
                return ResponseResult.error(ResponseEnum.DELETE_ERROR, "样片表中存在已生产样片数据");
            }

            tableDao.dropTable(tbSampleName);
        }

        // 删除批次数据
        int affectedRows = batchDao.deleteBatchByUid(uid);
        if (affectedRows == 0) {
            return ResponseResult.error(ResponseEnum.DELETE_ERROR);
        }

        // 删除用户权力中批次列表
        userPowerDao.deleteUserPowerListByBatchUid(uid);

        // 检查键是否存在，存在则删除当天批次Redis缓存
        if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(Constants.REDIS_KEY_TODAY_BATCH_UID_LIST))) {
            stringRedisTemplate.opsForSet().remove(Constants.REDIS_KEY_TODAY_BATCH_UID_LIST, String.valueOf(uid));
        }

        // 返回结果
        return ResponseResult.success(ResponseEnum.DELETE_SUCCESS);
    }


    /**
     * 更改批次
     */
    @Transactional
    public ResponseResult<?> updateBatch(Long uid, BatchReq batchReq) {
        // 检查批次是否存在
        BatchRes batchRes = batchDao.selectBatch(uid);
        if (batchRes == null) {
            return ResponseResult.error(ResponseEnum.DATA_NOT_EXISTS, "批次不存在");
        }

        // 旧数据
        String oldBatchId = batchRes.getBatchId();
        String oldTbSampleName = Constants.TB_SAMPLE_NAME_PREFIX + uid;
        Integer oldPreProductionSampleNum = batchRes.getPreProductionSampleNum();
        Integer oldStatus = batchRes.getStatus();

        // 新数据
        String newBatchId = batchReq.getBatchId();
        Integer newPreProductionSampleNum = batchReq.getPreProductionSampleNum();
        Integer newStatus = batchReq.getStatus();

        // 判断数据是否有变化
        if ((newBatchId == null || oldBatchId.equals(newBatchId)) &&
                (newPreProductionSampleNum == null || oldPreProductionSampleNum.equals(newPreProductionSampleNum)) &&
                (newStatus == null || oldStatus.equals(newStatus))) {
            return ResponseResult.error(ResponseEnum.DATA_NOT_CHANGE);
        }

        // 判断新批次号是否已存在
        if (newBatchId != null && !newBatchId.equals(oldBatchId) && batchDao.existsBatchId(newBatchId)) {
            return ResponseResult.error(ResponseEnum.DATA_ALREADY_EXISTS, "批次号已存在");
        }

        // 更新数据
        BatchDto batchDto = new BatchDto(uid, newBatchId, oldTbSampleName, newPreProductionSampleNum, newStatus);
        int affectedRows = batchDao.updateBatch(batchDto);
        if (affectedRows == 0) {
            return ResponseResult.error(ResponseEnum.UPDATE_ERROR);
        }

        // 判断表名是否有变化
//        if (!oldTbSampleName.equals(newTbSampleName)) {
//            if (tableDao.existTable(newTbSampleName) > 0) {
//                // 移除旧表
//                tableDao.dropTable(newTbSampleName);
//            }
//
//            if (tableDao.existTable(oldTbSampleName) > 0) {
//                // 重命名表
//                tableDao.renameTable(oldTbSampleName, newTbSampleName);
//            } else {
//                // 创建新表
//                tableDao.createSampleTable(newTbSampleName);
//            }
//        }

        // 返回结果
        return ResponseResult.success(ResponseEnum.UPDATE_SUCCESS);
    }


    /**
     * 查询单个批次
     */
    public ResponseResult<?> queryBatch(Long uid) {
        // 查询数据
        BatchRes batchRes = batchDao.selectBatch(uid);
        if (batchRes == null) {
            return ResponseResult.error(ResponseEnum.DATA_NOT_EXISTS, "批次不存在");
        }

        // 返回结果
        return ResponseResult.success(ResponseEnum.QUERY_SUCCESS, batchRes);
    }


    /**
     * 批量查询批次
     */
    public ResponseResult<?> queryBatchList(
            int pageNum, int pageSize, String batchId, List<Integer> status, Date startCreatedTime, Date endCreatedTime) {
        // 开启分页
        PageHelper.startPage(pageNum, pageSize);

        // 查询数据
        List<BatchRes> batchResList = batchDao.selectBatchList(batchId, status, startCreatedTime, endCreatedTime);

        // 查询当前用户拥有的批次
        Long loginUserUid = StpUtil.getLoginIdAsLong();
        UserEntity loginUserEntity = userDao.selectUser(loginUserUid);

        if (!loginUserEntity.getCategory().equals(UserCategoryEnum.SUPER_ADMIN.getValue())) {
            // 只获取当前用户拥有的批次数据
            List<Long> batchUidList = userPowerDao.selectAllBatchUid(loginUserUid);
            batchResList = batchResList.stream()
                    .filter(batchRes -> batchUidList.contains(batchRes.getUid()))
                    .collect(Collectors.toList());
        }

        // 加上已生产样片数量
        for (BatchRes batchRes : batchResList) {
            String tbSampleName = Constants.TB_SAMPLE_NAME_PREFIX + batchRes.getUid();
            if (tableDao.existTable(tbSampleName) > 0) {
                Integer sampleNum = tableDao.countRowsInTable(tbSampleName);
                batchRes.setFinishProductionSampleNum(sampleNum);
            }
        }

        // 返回结果
        return ResponseResult.success(ResponseEnum.QUERY_SUCCESS, new PageInfo<>(batchResList));
    }


    /**
     * 查询所有批次
     */
    public ResponseResult<?> queryAllBatch(
            List<Integer> status) {
        // 查询数据
        List<BatchRes> batchResList = batchDao.selectBatchList(null, status, null, null);

        // 查询当前用户拥有的批次
        Long loginUserUid = StpUtil.getLoginIdAsLong();
        UserEntity loginUserEntity = userDao.selectUser(loginUserUid);

        // 非管理员需要过滤
        if (!loginUserEntity.getCategory().equals(UserCategoryEnum.SUPER_ADMIN.getValue())) {
            // 只获取当前用户拥有的批次数据
            List<Long> batchUidList = userPowerDao.selectAllBatchUid(loginUserUid);
            batchResList = batchResList.stream()
                    .filter(batchRes -> batchUidList.contains(batchRes.getUid()))
                    .collect(Collectors.toList());
        }

        // 返回结果
        return ResponseResult.success(ResponseEnum.QUERY_SUCCESS, batchResList);
    }

}