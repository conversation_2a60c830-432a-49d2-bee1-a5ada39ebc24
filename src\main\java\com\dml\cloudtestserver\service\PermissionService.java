package com.dml.cloudtestserver.service;

import cn.hutool.core.util.IdUtil;
import com.dml.cloudtestserver.dao.PermissionDao;
import com.dml.cloudtestserver.model.permission.PermissionEntity;
import com.dml.cloudtestserver.model.permission.PermissionReq;
import com.dml.cloudtestserver.util.constants.PermissionScopeEnum;
import com.dml.cloudtestserver.util.response.ResponseEnum;
import com.dml.cloudtestserver.util.response.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Slf4j
@Service
public class PermissionService {

    @Autowired
    private PermissionDao permissionDao;

    private boolean checkFieldsRequired(PermissionReq req, String... fields) {
        for (String field : fields) {
            if (field.equals("code") && (req.getCode() == null || req.getCode().isEmpty())) {
                return false;
            }
        }
        return true;
    }

    private String checkFieldsNotPermitted(PermissionReq req, String... fields) {
        for (String field : fields) {
            if (field.equals("code") && req.getCode() != null) {
                return String.format("参数校验错误: %s, 字段不可填", "code");
            }
        }
        return null;
    }

    /**
     * 检查父权限类型与子权限类型匹配关系
     */
    private boolean checkScope(Integer scope, Integer parentScope) {
        if (parentScope == PermissionScopeEnum.DIR.getValue()) {
            // 目录子权限可以为: 目录、路由、功能
            return scope == PermissionScopeEnum.DIR.getValue()
                    || scope == PermissionScopeEnum.ROUTE.getValue()
                    || scope == PermissionScopeEnum.FUNC.getValue();
        } else if (parentScope == PermissionScopeEnum.ROUTE.getValue()) {
            // 路由子权限可以为: 功能
            return scope == PermissionScopeEnum.FUNC.getValue();
        } else if (parentScope == PermissionScopeEnum.FUNC.getValue()) {
            // 功能不能有子权限
            return false;
        } else {
            return false;
        }
    }


    /**
     * 添加权限
     */
    public ResponseResult<?> addPermission(PermissionReq permissionReq) {
        // 校验字段是否必填
        Integer scope = permissionReq.getScope();

        // 校验父权限是否不存在
        Long parentUid = permissionReq.getParentUid();

        System.out.println(parentUid);

        if (parentUid != null && parentUid != 1) {
            if (!permissionDao.existsUid(parentUid)) {
                return ResponseResult.error(ResponseEnum.DATA_NOT_EXISTS, "父权限不存在");
            } else {
                PermissionEntity permissionEntity = permissionDao.selectPermission(parentUid);
                Integer parentScope = permissionEntity.getScope();

                boolean flag = checkScope(scope, parentScope);
                if (!flag) {
                    return ResponseResult.error(ResponseEnum.PARAMETER_VALIDATION_ERROR, "父权限类型与当前权限类型不匹配");
                }
            }

        }

        // 校验是否已存在
        String name = permissionReq.getName();
        if (permissionDao.existsName(name)) {
            return ResponseResult.error(ResponseEnum.DATA_ALREADY_EXISTS, "权限名称已存在");
        } else if (scope == PermissionScopeEnum.FUNC.getValue()) {
            if (permissionDao.existsCode(permissionReq.getCode())) {
                return ResponseResult.error(ResponseEnum.DATA_ALREADY_EXISTS, "功能码已存在");
            }
        }

        // 校验排序权重是否正确
        Integer sortOrder = permissionReq.getSortOrder();
        List<Integer> sortOrders = permissionDao.selectAllSortOrderByParentUid(parentUid);

        if (sortOrder == 0) {
            if (sortOrders == null || sortOrders.isEmpty()) {
                sortOrder = 1;
            } else {
                // 排序权重等于sortOrders最大值+1
                sortOrder = Collections.max(sortOrders) + 1;
            }
        } else {
            if (sortOrders.contains(sortOrder)) {
                return ResponseResult.error(ResponseEnum.DATA_ALREADY_EXISTS, "排序权重已存在");
            }
        }

        // 数据对象
        Long uid = IdUtil.getSnowflakeNextId();
        PermissionEntity permissionEntity = new PermissionEntity(
                uid,
                scope,
                name,
                parentUid,
                permissionReq.getCode(),
                sortOrder);

        // 插入
        int affectedRows = permissionDao.insertPermission(permissionEntity);
        if (affectedRows == 0) {
            return ResponseResult.error(ResponseEnum.ADD_ERROR);
        }

        // 返回结果
        return ResponseResult.success(ResponseEnum.ADD_SUCCESS);
    }

    /**
     * 删除权限
     */
    public ResponseResult<?> deletePermission(Long uid) {
        // 检查权限是否存在
        PermissionEntity permissionEntity = permissionDao.selectPermission(uid);
        if (permissionEntity == null) {
            return ResponseResult.error(ResponseEnum.DATA_NOT_EXISTS, "权限不存在");
        }

        // 删除权限数据
        int affectedRows = permissionDao.deletePermission(uid);
        if (affectedRows == 0) {
            return ResponseResult.error(ResponseEnum.DELETE_ERROR);
        }

        // 返回结果
        return ResponseResult.success(ResponseEnum.DELETE_SUCCESS);
    }

    /**
     * 更改权限
     */
//    public ResponseResult<?> updatePermission(Long uid, PermissionReq permissionReq) {
//        // 检查权限是否存在
//        PermissionEntity oldPermissionEntity = permissionDao.selectPermission(uid);
//        if (oldPermissionEntity == null) {
//            return ResponseResult.error(ResponseEnum.DATA_NOT_EXISTS, "权限不存在");
//        }
//
//        // 旧数据
//        String oldName = oldPermissionEntity.getName();
//        Integer oldScope = oldPermissionEntity.getScope();
//        Long oldParentUid = oldPermissionEntity.getParentUid();
//        Integer oldSortOrder = oldPermissionEntity.getSortOrder();
//
//        // 新数据
//        String newName = permissionReq.getName();
//        Integer newScope = permissionReq.getScope();
//
//        // 检查字段是否必填
//        if (!newScope.equals(oldScope)) {
//            return ResponseResult.error(ResponseEnum.PARAMETER_VALIDATION_ERROR, "权限类型不允许更改");
//        }
//
//        // 校验是否已存在
//        String name = permissionReq.getName();
//        if (newName != null && !newName.equals(oldName) && permissionDao.existsName(name)) {
//            return ResponseResult.error(ResponseEnum.DATA_ALREADY_EXISTS, "权限名称已存在");
//        }
//
//        if (newScope == PermissionScopeEnum.ROUTE.getValue()) {
//        } else if (newScope == PermissionScopeEnum.FUNC.getValue()) {
//            String oldCode = oldPermissionEntity.getCode();
//            String newCode = permissionReq.getCode();
//
//            if (newCode != null && !newCode.equals(oldCode)
//                    && permissionDao.existsCode(permissionReq.getCode())) {
//                return ResponseResult.error(ResponseEnum.DATA_ALREADY_EXISTS, "功能码已存在");
//            }
//        }
//
//        // 校验排序权重是否正确
//        Integer sortOrder = permissionReq.getSortOrder();
//        List<Integer> sortOrders = permissionDao.selectAllSortOrderByParentUid(oldParentUid);
//
//        if (sortOrder == 0) {
//            if (sortOrders == null || sortOrders.isEmpty()) {
//                sortOrder = 1;
//            } else {
//                // 排序权重等于sortOrders最大值+1
//                sortOrder = Collections.max(sortOrders) + 1;
//            }
//        } else {
//            if (!Objects.equals(oldSortOrder, sortOrder) && sortOrders.contains(sortOrder)) {
//                return ResponseResult.error(ResponseEnum.DATA_ALREADY_EXISTS, "排序权重已存在");
//            }
//        }
//
//        // 更新数据
//        PermissionEntity newPermissionEntity = new PermissionEntity(
//                uid,
//                oldScope,
//                name,
//                oldParentUid,
//                permissionReq.getCode(),
//                sortOrder);
//
//        int affectedRows = permissionDao.updatePermission(newPermissionEntity);
//
//        if (affectedRows == 0) {
//            return ResponseResult.error(ResponseEnum.UPDATE_ERROR);
//        }
//
//        // 返回结果
//        return ResponseResult.success(ResponseEnum.UPDATE_SUCCESS);
//    }

    /**
     * 查询单个权限
     */
    public ResponseResult<?> queryPermission(Long uid) {
        // 查询数据
        PermissionEntity permissionEntity = permissionDao.selectPermission(uid);

        if (permissionEntity == null) {
            return ResponseResult.error(ResponseEnum.DATA_NOT_EXISTS, "权限不存在");
        }

        // 返回结果
        return ResponseResult.success(ResponseEnum.QUERY_SUCCESS, permissionEntity);
    }

    /**
     * 查询所有权限
     */
    public ResponseResult<?> queryAllPermission(String name) {
        // 查询数据
        List<PermissionEntity> permissionEntityList = permissionDao.selectAllPermission(name);

        // 返回结果
        return ResponseResult.success(ResponseEnum.QUERY_SUCCESS, permissionEntityList);
    }

}
