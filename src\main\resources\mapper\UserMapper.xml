<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dml.cloudtestserver.dao.UserDao">


    <!-- 结果映射，将数据库字段映射到 UserEntity 对象的属性 -->
    <resultMap id="BaseResultMap" type="com.dml.cloudtestserver.model.user.UserEntity">
        <result column="uid" property="uid"/>
        <result column="create_user_uid" property="createUserUid"/>
        <result column="account" property="account"/>
        <result column="password" property="password"/>
        <result column="status" property="status"/>
        <result column="category" property="category"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_time" property="updatedTime"/>
    </resultMap>

    <select id="existsUid" resultType="boolean">
        SELECT COUNT(*)
        FROM tb_user
        WHERE uid = #{uid}
    </select>

    <select id="existsAccount" resultType="boolean">
        SELECT COUNT(*)
        FROM tb_user
        WHERE account = #{account}
    </select>

    <insert id="insertUser" parameterType="com.dml.cloudtestserver.model.user.UserInsertDto">
        INSERT INTO tb_user (uid, create_user_uid, account, password, status, category)
        VALUES (#{uid}, #{createUserUid}, #{account}, #{password}, #{status}, #{category})
    </insert>

    <update id="updateUser" parameterType="com.dml.cloudtestserver.model.user.UserUpdateDto">
        UPDATE tb_user
        SET account = #{account},
            status  = #{status}
        WHERE uid = #{uid}
    </update>

    <update id="updatePassword" parameterType="com.dml.cloudtestserver.model.auth.UpdatePasswordDto">
        UPDATE tb_user
        SET password = #{newPassword}
        WHERE uid = #{uid}
    </update>


    <select id="selectUser" parameterType="Long" resultMap="BaseResultMap">
        SELECT uid,
               create_user_uid,
               account,
               password,
               status,
               category,
               created_time,
               updated_time
        FROM tb_user
        WHERE uid = #{uid}
    </select>


    <select id="selectUserByAccount" resultMap="BaseResultMap">
        SELECT uid,
               create_user_uid,
               account,
               password,
               status,
               category,
               created_time,
               updated_time
        FROM tb_user
        WHERE account = #{account}
    </select>

    <select id="selectUserList" resultMap="BaseResultMap">
        SELECT uid, create_user_uid, account, password, status, category, created_time, updated_time
        FROM tb_user
        <where>
            <if test="createUserAccount != null and createUserAccount != ''">
                AND create_user_uid IN (
                SELECT uid FROM tb_user WHERE account LIKE CONCAT('%', #{createUserAccount}, '%')
                )
            </if>

            <if test="category != null and category.size() > 0">
                AND (category IN
                <foreach item="item" index="index" collection="category" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>

            <if test="account != null and account != ''">
                AND account LIKE CONCAT('%', #{account}, '%')
            </if>

            <if test="status != null and status.size() > 0">
                AND (status IN
                <foreach item="item" index="index" collection="status" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
        </where>
    </select>


    <delete id="deleteUser" parameterType="Long">
        DELETE
        FROM tb_user
        WHERE uid = #{uid}
    </delete>


</mapper>
