package com.dml.cloudtestserver.model.permission;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 权限表实体
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PermissionEntity implements Serializable {

    private static final long serialVersionUID = -3904976370764977311L;

    /**
     * UID
     */
    private Long uid;

    /**
     * 权限范围
     */
    private Integer scope;

    /**
     * 权限名称
     */
    private String name;

    /**
     * 父权限UID
     */
    private Long parentUid;

    /**
     * 权限码
     */
    private String code;

    /**
     * 排序权重
     */
    private Integer sortOrder;
}