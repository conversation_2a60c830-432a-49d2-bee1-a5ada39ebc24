package com.dml.cloudtestserver.model.user_power;

import com.dml.cloudtestserver.util.constants.PowerScopeEnum;
import com.dml.cloudtestserver.util.tool.LongDeserializer;
import com.dml.cloudtestserver.util.validator.ValidEnum;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserPowerReq implements Serializable {

    private static final long serialVersionUID = -3982243083159452437L;

    /**
     * 权力范围
     */
    @Schema(description = "权力范围。可选值：1-权限、2-批次")
    @NotNull(message = "权力范围不能为空")
    @ValidEnum(enumClass = PowerScopeEnum.class, message = "可选值：1-权限、2-批次")
    private Integer scope;

    /**
     * 权力UID
     */
    @JsonDeserialize(using = LongDeserializer.class)
    @NotNull(message = "权力UID不能为空")
    private Long powerUid;
}
