package com.dml.cloudtestserver.controller.handler;

import com.dml.cloudtestserver.dao.BatchDao;
import com.dml.cloudtestserver.dao.SampleDao;
import com.dml.cloudtestserver.dao.TableDao;
import com.dml.cloudtestserver.model.batch.BatchRes;
import com.dml.cloudtestserver.model.sample.PerCapacitySampleCountDto;
import com.dml.cloudtestserver.util.constants.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 当天不同容量样片数
 */
@Slf4j
@Service
public class TodayDiffCapacitySampleNumDataHandler extends BaseWebSocketHandler {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private BatchDao batchDao;

    @Autowired
    private SampleDao sampleDao;

    @Autowired
    private TableDao tableDao;

    /**
     * 遍历当天批次UID列表，获取每个批次的每小时的样片数、样片成功率
     */
    @Override
    public Map<String, Object> getData() {
        // 获取当天批次UID列表
        Set<Long> todayBatchUid = getTodayBatchUidList();
        System.out.println("todayBatchUid -- " + todayBatchUid);

        // 所有批次的每小时量产结果
        List<Map<String, Object>> dataList = new ArrayList<>();

        // 获取每个批次的样片数、样片成功率
        for (Long batchUid : todayBatchUid) {
            if (!batchDao.existsUid(batchUid)) {
                continue;
            }

            BatchRes batchRes = batchDao.selectBatch(batchUid);
            if (batchRes == null) {
                continue;
            }

            // 获取批次ID
            String tbSampleName = Constants.TB_SAMPLE_NAME_PREFIX + batchRes.getUid();

            if (tableDao.existTable(tbSampleName) == 0) {
                continue;
            }

            Map<String, Object> map = new ConcurrentHashMap<>();
            map.put("batchUid", batchUid);
            map.put("batchId", batchRes.getBatchId());

            // 不同容量样片数
            List<PerCapacitySampleCountDto> todayDiffCapacitySampleNum = sampleDao.countSampleNumByCapacity(tbSampleName);
            map.put("todayDiffCapacitySampleNum", todayDiffCapacitySampleNum);

            dataList.add(map);
        }

        Map<String, Object> data = new ConcurrentHashMap<>();
//        data.put("todayBatchNum", todayBatchUid.size());
        data.put("dataList", dataList);

        return data;
    }


    /**
     * 获取当天批次 UID 列表
     *
     * @return 当天批次 UID 列表（长整型集合）
     */
    public Set<Long> getTodayBatchUidList() {
        Set<String> stringSet = stringRedisTemplate.opsForSet().members(Constants.REDIS_KEY_TODAY_BATCH_UID_LIST);
        if (stringSet != null) {
            return stringSet.stream()
                    .map(s -> {
                        try {
                            return Long.parseLong(s);
                        } catch (NumberFormatException e) {
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
        } else {
            log.error("没有获取到有效的集合数据");
            return new HashSet<>();
        }
    }


}
