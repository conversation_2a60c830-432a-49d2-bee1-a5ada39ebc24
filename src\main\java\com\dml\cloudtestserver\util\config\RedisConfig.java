package com.dml.cloudtestserver.util.config;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.jsontype.impl.LaissezFaireSubTypeValidator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

@Configuration
public class RedisConfig {
    /**
     * 配置并返回RedisTemplate实例
     * 该方法配置了一个用于操作Redis的模板，特别指定了key和value的序列化方式
     * 使用了Spring Data Redis提供的RedisTemplate类，该类提供了操作Redis的数据访问对象
     *
     * @param factory Redis连接工厂，用于建立与Redis服务器的连接
     * @return 配置好的RedisTemplate实例，用于操作Redis
     */
    @Bean(name = "redisTemplate")
    public RedisTemplate<String, Object> getRedisTemplate(RedisConnectionFactory factory) {
        // 创建RedisTemplate实例
        RedisTemplate<String, Object> redisTemplate = new RedisTemplate<>();

        // 设置Redis连接工厂
        redisTemplate.setConnectionFactory(factory);

        // 序列化器
        StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();  // StringRedisSerializer
        Jackson2JsonRedisSerializer<Object> jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer<>(Object.class);  // Jackson2JsonRedisSerializer

        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY); // 在序列化时可以检测到对象的所有属性
        objectMapper.activateDefaultTyping(LaissezFaireSubTypeValidator.instance,
                ObjectMapper.DefaultTyping.NON_FINAL, JsonTypeInfo.As.PROPERTY);  // 在反序列化时可以识别不同类型的对象
        jackson2JsonRedisSerializer.setObjectMapper(objectMapper);  // 设置ObjectMapper到Jackson2JsonRedisSerializer

        // 定义序列化方式
        redisTemplate.setKeySerializer(stringRedisSerializer);  // key采用String的序列化方式
        redisTemplate.setValueSerializer(jackson2JsonRedisSerializer);  // value序列化方式采用jackson
        redisTemplate.setHashKeySerializer(stringRedisSerializer); // hash的key也采用String的序列化方式
        redisTemplate.setHashValueSerializer(jackson2JsonRedisSerializer); // hash的value序列化方式采用jackson

        // 调用afterPropertiesSet方法，确保所有配置生效
        redisTemplate.afterPropertiesSet();

        // 返回配置好的RedisTemplate实例
        return redisTemplate;
    }

}
