package com.dml.cloudtestserver.controller;


import cn.dev33.satoken.annotation.SaCheckPermission;
import com.dml.cloudtestserver.model.sample.SampleInsertReq;
import com.dml.cloudtestserver.model.sample.SampleUpdateReq;
import com.dml.cloudtestserver.service.SampleService;
import com.dml.cloudtestserver.util.constants.Constants;
import com.dml.cloudtestserver.util.response.ResponseResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Tag(name = "样片管理")
@RestController
@RequestMapping("/api/sample")
public class SampleController {

    @Autowired
    private SampleService sampleService;


    @Operation(summary = "添加样片量产信息")
    @PostMapping("/{batchUid}")
    @SaCheckPermission("sample-add")
    public ResponseResult<?> addSample(
            @Parameter(description = "批次UID")
            @PathVariable
            Long batchUid,

            @Schema(implementation = SampleInsertReq.class)
            @Valid
            @RequestBody
            SampleInsertReq sampleInsertReq) {
        return sampleService.addSample(batchUid, sampleInsertReq);
    }


    @Operation(summary = "更改样片日志上传状态")
    @PutMapping("/{batchUid}/{uid}")
    @SaCheckPermission("sample-update")
    public ResponseResult<?> updateSample(
            @Parameter(description = "批次UID")
            @PathVariable
            Long batchUid,

            @Parameter(description = "UID")
            @PathVariable
            Long uid,

            @Schema(implementation = SampleUpdateReq.class)
            @Valid
            @RequestBody
            SampleUpdateReq sampleUpdateReq) {
        return sampleService.updateSample(batchUid, uid, sampleUpdateReq);
    }


//    @Operation(summary = "查询单个样片量产信息")
//    @GetMapping("/{batchUid}/{uid}")
//    @SaCheckPermission("sample-query")
//    public ResponseResult<?> querySample(
//            @Parameter(description = "批次UID") @PathVariable Long batchUid,
//            @Parameter(description = "UID") @PathVariable Long uid) {
//        return sampleService.querySample(batchUid, uid);
//    }


    @Operation(summary = "批量查询样片量产信息")
    @GetMapping("")
    @SaCheckPermission("sample-query")
    public ResponseResult<?> querySampleList(
            @Parameter(description = "页码")
            @RequestParam(value = "pageNum", defaultValue = Constants.DEFAULT_PAGE_NUM)
            Integer pageNum,

            @Parameter(description = "每页数量")
            @RequestParam(value = "pageSize", defaultValue = Constants.DEFAULT_PAGE_SIZE)
            Integer pageSize,

            @Parameter(description = "批次UID")
            @RequestParam(value = "batchUid", required = false)
            Long batchUid,

            @Parameter(description = "样片号")
            @RequestParam(value = "sampleId", required = false)
            String sampleId,

            @Parameter(description = "产品类型")
            @RequestParam(value = "productType", required = false)
            List<Integer> productType,

            @Parameter(description = "量产结果")
            @RequestParam(value = "productionResult", required = false)
            List<Integer> productionResult,

            @Parameter(description = "日志上传状态")
            @RequestParam(value = "logUploadStatus", required = false)
            List<Integer> logUploadStatus
//            @Parameter(description = "起始创建时间 格式：yyyy-MM-dd HH:mm:ss") @RequestParam(value = "startCreatedTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startCreatedTime,
//            @Parameter(description = "结束创建时间 格式：yyyy-MM-dd HH:mm:ss") @RequestParam(value = "endCreatedTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endCreatedTime
    ) {
        // 时间逻辑检查
//        if (startCreatedTime != null && endCreatedTime != null && startCreatedTime.after(endCreatedTime)) {
//            return ResponseResult.error(ResponseEnum.PARAMETER_VALIDATION_ERROR, "起始创建时间不能晚于结束创建时间");
//        }

        return sampleService.querySampleList(
                pageNum, pageSize, batchUid, sampleId, productType, productionResult, logUploadStatus, null, null);
    }


//    @Operation(summary = "查询批次下所有样片号")
//    @GetMapping("/queryBatchAllSampleId/{batchUid}")
//    @SaCheckPermission("sample-query")
//    public ResponseResult<?> queryBatchAllSampleId(
//            @Parameter(description = "批次UID") @PathVariable Long batchUid,
//            @Parameter(description = "页码") @RequestParam(value = "pageNum", defaultValue = Constants.DEFAULT_PAGE_NUM) Integer pageNum,
//            @Parameter(description = "每页数量") @RequestParam(value = "pageSize", defaultValue = Constants.DEFAULT_PAGE_SIZE) Integer pageSize) {
//        return sampleService.queryBatchAllSampleId(batchUid, pageNum, pageSize);
//    }


    @Operation(summary = "下载最新日志文件")
    @GetMapping("/downloadLatestLog/{sampleId}")
    @SaCheckPermission("download-log")
    public ResponseEntity<?> downloadLatestLog(@PathVariable String sampleId) {
        return sampleService.downloadLatestLog(sampleId);
    }


    @Operation(summary = "下载日志文件")
    @GetMapping("/downloadLog/{batchUid}/{sampleUid}")
    @SaCheckPermission("download-log")
    public ResponseEntity<?> downloadLog(@PathVariable Long batchUid, @PathVariable Long sampleUid) {
        return sampleService.downloadLog(batchUid, sampleUid);
    }


}



