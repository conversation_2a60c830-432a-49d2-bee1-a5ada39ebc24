package com.dml.cloudtestserver.dao;

import com.dml.cloudtestserver.model.permission.FuncEntity;
import com.dml.cloudtestserver.model.permission.PermissionEntity;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface PermissionDao {

    // UID是否存在
    boolean existsUid(Long uid);

    // 父权限是否存在
    boolean existsParentUid(Long parentUid);

    // 权限名称是否存在
    boolean existsName(String name);

    // 权限码是否存在
    boolean existsCode(String code);

    // 插入权限
    int insertPermission(PermissionEntity permissionEntity);

    // 删除权限
    int deletePermission(Long uid);

    // 更新权限
    int updatePermission(PermissionEntity permissionEntity);

    // 查询权限
    PermissionEntity selectPermission(Long uid);

    // 根据权限码查询权限
    PermissionEntity selectPermissionByFuncCode(String funcCode);

    // 查询所有权限
    List<PermissionEntity> selectAllPermission(String name);

    // 查询功能列表
    List<FuncEntity> selectFuncList();

    // 查询同个父权限下的所有权限排序
    List<Integer> selectAllSortOrderByParentUid(Long parentUid);

}
