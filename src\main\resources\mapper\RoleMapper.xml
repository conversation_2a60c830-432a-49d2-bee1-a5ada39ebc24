<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dml.cloudtestserver.dao.RoleDao">


    <!-- 结果映射，将数据库字段映射到 UserEntity 对象的属性 -->
    <resultMap id="BaseResultMap" type="com.dml.cloudtestserver.model.role.RoleEntity">
        <result column="uid" property="uid"/>
        <result column="name" property="name"/>
    </resultMap>

    <select id="existsUid" parameterType="long" resultType="boolean">
        SELECT EXISTS (SELECT 1
                       FROM tb_role
                       WHERE uid = #{uid})
    </select>

    <select id="existsName" parameterType="string" resultType="boolean">
        SELECT EXISTS (SELECT 1
                       FROM tb_role
                       WHERE name = #{name})
    </select>

    <insert id="insertRole" parameterType="com.dml.cloudtestserver.model.role.RoleEntity">
        INSERT INTO tb_role (uid, name)
        VALUES (#{uid}, #{name})
    </insert>


    <select id="selectRole" parameterType="long" resultType="com.dml.cloudtestserver.model.role.RoleEntity">
        SELECT *
        FROM tb_role
        WHERE uid = #{uid}
    </select>

    <delete id="deleteRole" parameterType="long">
        DELETE
        FROM tb_role
        WHERE uid = #{uid}
    </delete>

    <update id="updateRole" parameterType="com.dml.cloudtestserver.model.role.RoleEntity">
        UPDATE tb_role
        SET name = #{name}
        WHERE uid = #{uid}
    </update>

    <select id="selectRoleList" resultType="com.dml.cloudtestserver.model.role.RoleEntity">
        SELECT uid, name
        FROM tb_role
        <where>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
        </where>
    </select>


</mapper>
