package com.dml.cloudtestserver.util.tool;

import cn.dev33.satoken.stp.StpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import java.io.IOException;
import java.net.URI;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
public class VerifyWebSocketToken {
    public boolean verify(WebSocketSession session) throws IOException {
        boolean flag = true;

        // 获取 URI
        URI uri = Objects.requireNonNull(session.getUri());
        String query = uri.getQuery();

        // 解析查询参数
        Map<String, List<String>> queryPairs = parseQuery(query);
        List<String> tokenList = queryPairs.get("token");

        // 获取token
        String token = Objects.requireNonNull(session.getUri()).getQuery();
        System.out.println(token);

        // 校验token
        Object loginId = StpUtil.getLoginIdByToken(token);
        if (loginId == null) {
            flag = false;
            session.close();
            session.sendMessage(new TextMessage("无效token"));
            log.error("无效token");
        }

        return flag;
    }

    // 解析查询参数的方法
    private Map<String, List<String>> parseQuery(String query) {
        return Arrays.stream(query.split("&"))
                .map(param -> param.split("="))
                .collect(Collectors.groupingBy(
                        arr -> arr[0],
                        Collectors.mapping(arr -> arr.length > 1 ? arr[1] : "", Collectors.toList())
                ));
    }
}
