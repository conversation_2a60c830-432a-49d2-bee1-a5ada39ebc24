##### 测试环境 - 本地容器版本 #####

# 自定义配置
custom-config:
  server-ip: localhost
  mp-log-repo: /u-test-2/MPTOOL_LOG_DATA
  mp-log-suffix: .zip
  websocket-update-frequency: 5


# 日志配置
logging:
  level:
    "[com.dml.cloudtestserver]": debug
  file:
    path: /logs


# FTP配置
ftp:
  host: localhost
  port: 21
  account: 古永浩
  password: MIMA100dml
  key: "pekho8ff8imnf573j3f3"


spring:
  # MySQL
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ******************************************************************************************************************
    username: db_cloud_test_user
    password: db_cloud_test_pwd
    # druid-spring-boot-starter 依赖自动生效 druid，可以不配置 type 属性，但建议配置
    type: com.alibaba.druid.pool.DruidDataSource

  # Redis 配置
  redis:
    host: localhost
    port: 6379
    database: 1 # 数据库
    password: # 密码 - 本地Redis无密码
    timeout: 60000  # 超时时间
    lettuce:
      pool:
        max-active: 8 # 连接池最大连接数（使用负值表示没有限制） 默认 8
        max-wait: -1 # 连接池最大阻塞等待时间（使用负值表示没有限制） 默认 -1
        max-idle: 8 # 连接池中的最大空闲连接 默认 8
        min-idle: 0 # 连接池中的最小空闲连接 默认 0
