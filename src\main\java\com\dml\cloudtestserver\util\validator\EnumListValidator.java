package com.dml.cloudtestserver.util.validator;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.lang.reflect.Method;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class EnumListValidator implements ConstraintValidator<ValidEnumList, List<Integer>> {

    private Set<Integer> allowedValues; // 存储合法值
    private String errorMessage;        // 自定义错误信息

    @Override
    public void initialize(ValidEnumList constraintAnnotation) {
        Class<? extends Enum<?>> enumClass = constraintAnnotation.enumClass(); // 获取枚举类
        errorMessage = constraintAnnotation.message(); // 获取自定义错误信息

        // 通过反射获取枚举的所有值
        try {
            Method getValueMethod = enumClass.getMethod("getValue"); // 假定枚举有 getValue 方法
            allowedValues = new HashSet<>();
            for (Enum<?> enumConstant : enumClass.getEnumConstants()) {
                allowedValues.add((Integer) getValueMethod.invoke(enumConstant));
            }
        } catch (Exception e) {
            throw new IllegalArgumentException("枚举类必须包含一个 'getValue' 方法", e);
        }
    }

    @Override
    public boolean isValid(List<Integer> statusList, ConstraintValidatorContext context) {
        if (statusList == null) {
            return true; // 允许空值，认为是合法的
        }

        // 校验每个值是否在合法值集合中
        for (Integer status : statusList) {
            if (!allowedValues.contains(status)) {
                // 设置自定义错误信息
                context.disableDefaultConstraintViolation();
                context.buildConstraintViolationWithTemplate(errorMessage).addConstraintViolation();
                return false;
            }
        }
        return true;
    }
}
