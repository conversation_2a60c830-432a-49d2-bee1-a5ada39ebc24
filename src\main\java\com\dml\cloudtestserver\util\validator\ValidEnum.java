package com.dml.cloudtestserver.util.validator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 自定义注解，用于校验一个字段或参数的值是否在指定的枚举类型中。
 */
@Target({ElementType.FIELD, ElementType.PARAMETER})  // 注解的作用范围，可以应用于字段或方法参数。
@Retention(RetentionPolicy.RUNTIME)   // 注解的生命周期，RUNTIME 表示注解会在运行时保留，允许反射机制读取。
@Constraint(validatedBy = EnumValidator.class)  // 指定了用于处理该注解的校验器类，这里是 EnumValidator。
public @interface ValidEnum {
    /**
     * 指定枚举类型的类。注解会通过这个类来校验字段或参数的值。
     *
     * @return 枚举类
     */
    Class<? extends Enum<?>> enumClass();

    /**
     * 校验失败时的默认错误消息。
     *
     * @return 错误消息
     */
    String message() default "不支持的枚举值";

    /**
     * 组信息，用于分组校验。
     *
     * @return 组
     */
    Class<?>[] groups() default {};

    /**
     * 负载信息，用于传递附加的元数据。
     *
     * @return 负载
     */
    Class<? extends Payload>[] payload() default {};
}
