package com.dml.cloudtestserver.util.exception;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.exception.NotPermissionException;
import com.dml.cloudtestserver.util.response.ResponseEnum;
import com.dml.cloudtestserver.util.response.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.servlet.NoHandlerFoundException;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.Objects;
import java.util.Set;

@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {


    /**
     * 资源不存在
     */
    @ExceptionHandler(NoHandlerFoundException.class)
    public ResponseResult<?> handleNoHandlerFoundException(NoHandlerFoundException e, HttpServletRequest request) {
        log.error("请求地址: {}, 请求方法: {}, 资源不存在: {}", request.getRequestURI(), request.getMethod(), e.getMessage(), e);
        return ResponseResult.error(ResponseEnum.RESOURCE_NOT_EXISTS);
    }


    /**
     * 未能读取到有效Token
     */
    @ExceptionHandler(NotLoginException.class)
    public ResponseResult<?> handleNotLoginException(NotLoginException e, HttpServletRequest request) {
        ResponseEnum responseEnum;

        // 根据异常类型设置相应的响应枚举
        if (e.getType().equals(NotLoginException.NOT_TOKEN)) {
            // 如果异常类型为 "NOT_TOKEN"，表示请求中没有提供Token
            responseEnum = ResponseEnum.NO_TOKEN_ERROR;
        } else if (e.getType().equals(NotLoginException.INVALID_TOKEN)) {
            // 如果异常类型为 "INVALID_TOKEN"，表示提供的Token无效
            responseEnum = ResponseEnum.INVALID_TOKEN_ERROR;
        } else if (e.getType().equals(NotLoginException.TOKEN_TIMEOUT)) {
            // 如果异常类型为 "TOKEN_TIMEOUT"，表示Token已过期
            responseEnum = ResponseEnum.TOKEN_TIMEOUT_ERROR;
        } else {
            // 其他未明确分类的登录异常，统一处理为 "NOT_LOGIN_ERROR"
            responseEnum = ResponseEnum.NOT_LOGIN_ERROR;
        }

        log.error("请求地址: {}, 请求方法: {}, 未能读取到有效Token: {}", request.getRequestURI(), request.getMethod(), e.getMessage(), e);
        return ResponseResult.error(responseEnum);
    }


    /**
     * 请求方法错误
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public ResponseResult<?> handleMethodNotSupportedException(HttpRequestMethodNotSupportedException e, HttpServletRequest request) {
        log.error("请求地址: {}, 请求方法: {}, 请求方法错误: {}", request.getRequestURI(), request.getMethod(), e.getMethod(), e);
        return ResponseResult.error(ResponseEnum.METHOD_NOT_SUPPORTED);
    }

    /**
     * 参数类型不匹配
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ResponseResult<?> handleMethodArgumentTypeMismatchException(MethodArgumentTypeMismatchException e, HttpServletRequest request) {
        log.error("请求地址: {}, 请求方法: {}, 参数类型不匹配: {}", request.getRequestURI(), request.getMethod(), e.getMessage(), e);
        return ResponseResult.error(ResponseEnum.METHOD_ARGUMENT_TYPE_MISMATCH);
    }


    /**
     * 无权限
     */
    @ExceptionHandler(NotPermissionException.class)
    public ResponseResult<?> handleNotPermissionException(NotPermissionException e, HttpServletRequest request) {
        log.error("请求地址: {}, 请求方法: {}, 无权限: {}", request.getRequestURI(), request.getMethod(), e.getMessage(), e);
        return ResponseResult.error(ResponseEnum.NO_PERMISSION_ERROR);
    }


    /**
     * 不支持的媒体类型异常
     */
    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    public ResponseResult<?> handleHttpMediaTypeNotSupportedException(HttpMediaTypeNotSupportedException e, HttpServletRequest request) {
        log.error("请求地址: {}, 请求方法: {}, 不支持的媒体类型: {}", request.getRequestURI(), request.getMethod(), e.getMessage(), e);
        return ResponseResult.error(ResponseEnum.MEDIA_TYPE_NOT_SUPPORTED, e.getMessage());
    }


    /**
     * 请求体为空、请求体格式不正确
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ResponseResult<?> handleHttpMessageNotReadable(HttpMessageNotReadableException e, HttpServletRequest request) {

        // 判断是否是请求体为空
        if (Objects.requireNonNull(e.getMessage()).contains("Required request body is missing")) {
            log.error("请求地址: {}, 请求方法: {}, 请求体为空: {}", request.getRequestURI(), request.getMethod(), e.getMessage(), e);
            return ResponseResult.error(ResponseEnum.REQUEST_BODY_EMPTY, "请求体为空");
        } else {
            log.error("请求地址: {}, 请求方法: {}, 请求体格式不正确: {}", request.getRequestURI(), request.getMethod(), e.getMessage(), e);
            String msg = "请求体格式不正确: " + e.getMessage();
            return ResponseResult.error(ResponseEnum.PARAMETER_VALIDATION_ERROR, msg);
        }
    }


    /**
     * 参数校验异常处理
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseResult<?> handleMethodArgumentNotValidException(MethodArgumentNotValidException e, HttpServletRequest request) {
        log.error("请求地址: {}, 请求方法: {}, 参数校验错误: {}", request.getRequestURI(), request.getMethod(), e.getMessage(), e);

        BindingResult bindingResult = e.getBindingResult();
        if (bindingResult.hasErrors()) {

            // 只获取第1个错误信息
            FieldError firstError = bindingResult.getFieldErrors().get(0);
            String fieldName = firstError.getField();
            String errorMessage = firstError.getDefaultMessage();
            String msg = String.format("参数校验错误：%s, %s", fieldName, errorMessage);

            return ResponseResult.error(ResponseEnum.PARAMETER_VALIDATION_ERROR, msg);

            // 返回所有错误信息
//            List<Map<String, String>> errors = bindingResult.getFieldErrors().stream()
//                    .map(fieldError -> {
//                        Map<String, String> error = new HashMap<>();
//                        error.put("field", fieldError.getField());
//                        error.put("errMsg", fieldError.getDefaultMessage());
//                        return error;
//                    })
//                    .collect(Collectors.toList());
//
//            return ResponseResult.error(ResponseEnum.PARAMETER_VALIDATION_ERROR, errors);


        } else {
            return ResponseResult.error(ResponseEnum.PARAMETER_VALIDATION_ERROR, "未知参数错误");
        }
    }

    /**
     * 上传大小超限异常
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public ResponseResult<?> handleMaxUploadSizeExceeded(Exception e, HttpServletRequest request) {
        String msg = "文件上传大小超出限制：" + e.getMessage();
        log.error("请求地址: {}, 请求方法: {}, 上传大小超限异常: {}", request.getRequestURI(), request.getMethod(), msg, e);
        return ResponseResult.error(ResponseEnum.PAYLOAD_TOO_LARGE);
    }

    /**
     * 参数校验失败的异常处理
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseResult<?> handleConstraintViolationException(ConstraintViolationException e, HttpServletRequest request) {
        // 记录日志，打印请求地址、请求方法及异常信息
        log.error("请求地址: {}, 请求方法: {}, 参数校验失败: {}", request.getRequestURI(), request.getMethod(), e.getMessage(), e);

        // 获取所有的参数校验违规信息
        Set<ConstraintViolation<?>> violations = e.getConstraintViolations();

        // 如果存在校验违规
        if (!violations.isEmpty()) {
            // 创建一个 StringBuilder 来构建自定义的错误信息，包含字段名和校验失败的信息
            StringBuilder errorMessage = new StringBuilder();

            // 遍历所有的违规信息
            for (ConstraintViolation<?> violation : violations) {
                // 提取字段名（属性路径）
                String fieldName = violation.getPropertyPath().toString();

                // 清理字段名，去掉方法名（例如 'queryAllBatch'）
                // 假设字段名始终是路径的最后一部分
                String[] pathParts = fieldName.split("\\.");
                fieldName = pathParts[pathParts.length - 1];  // 只获取最后一部分（字段名）

                // 为该字段构建错误信息（可以根据需要自定义信息）
                errorMessage.append(fieldName).append(": ").append(violation.getMessage()).append("; ");
            }

            // 返回自定义的错误信息
            String msg = String.format("参数校验错误：%s", errorMessage.toString().trim());
            return ResponseResult.error(ResponseEnum.PARAMETER_VALIDATION_ERROR, msg);
        }

        // 如果没有校验违规，返回默认的错误信息
        return ResponseResult.error(ResponseEnum.PARAMETER_VALIDATION_ERROR, e.getMessage());
    }


    /**
     * 处理所有未被捕获的异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseResult<?> handleGenericException(Exception e, HttpServletRequest request) {
        log.error("请求地址: {}, 请求方法: {}, 未被捕获的异常: {}", request.getRequestURI(), request.getMethod(), e.getMessage(), e);
        return ResponseResult.error(ResponseEnum.INTERNAL_SERVER_ERROR);
    }


}
