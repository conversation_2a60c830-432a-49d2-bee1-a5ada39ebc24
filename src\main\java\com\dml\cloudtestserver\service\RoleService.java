package com.dml.cloudtestserver.service;

import cn.hutool.core.util.IdUtil;
import com.dml.cloudtestserver.dao.RoleDao;
import com.dml.cloudtestserver.model.role.RoleEntity;
import com.dml.cloudtestserver.model.role.RoleReq;
import com.dml.cloudtestserver.util.response.ResponseEnum;
import com.dml.cloudtestserver.util.response.ResponseResult;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j
@Service
public class RoleService {

    @Autowired
    private RoleDao roleDao;


    /**
     * 添加角色
     */
    public ResponseResult<?> addRole(RoleReq roleReq) {
        // 数据
        String name = roleReq.getName();

        // 判断姓名是否已存在
        if (roleDao.existsName(name)) {
            return ResponseResult.error(ResponseEnum.DATA_ALREADY_EXISTS, "角色名称已存在");
        }

        // 数据对象
        Long uid = IdUtil.getSnowflakeNextId();
        RoleEntity roleEntity = new RoleEntity(uid, name);

        // 插入
        int affectedRows = roleDao.insertRole(roleEntity);
        if (affectedRows == 0) {
            return ResponseResult.error(ResponseEnum.ADD_ERROR);
        }

        // 返回结果
        return ResponseResult.success(ResponseEnum.ADD_SUCCESS, roleEntity);
    }


    /**
     * 删除角色
     */
    public ResponseResult<?> deleteRole(Long uid) {
        // 检查角色是否存在
        RoleEntity roleEntity = roleDao.selectRole(uid);
        if (roleEntity == null) {
            return ResponseResult.error(ResponseEnum.DATA_NOT_EXISTS, "角色不存在");
        }

        // 删除角色数据
        int affectedRows = roleDao.deleteRole(uid);
        if (affectedRows == 0) {
            return ResponseResult.error(ResponseEnum.DELETE_ERROR);
        }

        // 返回结果
        return ResponseResult.success(ResponseEnum.DELETE_SUCCESS);
    }


    /**
     * 更改角色
     */
    public ResponseResult<?> updateRole(Long uid, RoleReq roleReq) {
        // 检查角色是否存在
        RoleEntity roleEntity = roleDao.selectRole(uid);
        if (roleEntity == null) {
            return ResponseResult.error(ResponseEnum.DATA_NOT_EXISTS, "角色不存在");
        }

        // 旧数据
        String oldName = roleEntity.getName();

        String newName = roleReq.getName();

        // 判断是否数据未改变
        if ((newName == null || oldName.equals(newName))) {
            return ResponseResult.error(ResponseEnum.DATA_NOT_CHANGE);
        }

        // 判断角色名称是否存在
        if (roleDao.existsName(newName)) {
            return ResponseResult.error(ResponseEnum.DATA_ALREADY_EXISTS, "角色名称已存在");
        }

        // 更新数据
        roleEntity = new RoleEntity(uid, newName);
        int affectedRows = roleDao.updateRole(roleEntity);
        if (affectedRows == 0) {
            return ResponseResult.error(ResponseEnum.UPDATE_ERROR);
        }

        // 返回结果
        return ResponseResult.success(ResponseEnum.UPDATE_SUCCESS, roleEntity);
    }


    /**
     * 查询单个角色
     */
    public ResponseResult<?> queryRole(Long uid) {
        // 检查角色是否存在
        RoleEntity roleEntity = roleDao.selectRole(uid);
        if (roleEntity == null) {
            return ResponseResult.error(ResponseEnum.DATA_NOT_EXISTS, "角色不存在");
        }

        // 返回结果
        return ResponseResult.success(ResponseEnum.QUERY_SUCCESS, roleEntity);
    }


    /**
     * 批量查询角色
     */
    public ResponseResult<?> queryAllRole(int pageNum, int pageSize, String name) {
        // 开启分页
        PageHelper.startPage(pageNum, pageSize);

        // 查询数据
        List<RoleEntity> roleEntityList = roleDao.selectRoleList(name);

        // 返回结果
        return ResponseResult.success(ResponseEnum.QUERY_SUCCESS, roleEntityList);
    }

}
