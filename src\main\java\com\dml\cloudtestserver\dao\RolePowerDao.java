package com.dml.cloudtestserver.dao;

import com.dml.cloudtestserver.model.role_power.RolePowerDto;
import com.dml.cloudtestserver.model.role_power.RolePowerEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface RolePowerDao {

    // UID是否存在
    boolean existsUid(Long uid);

    // UID和权力UID是否存在
    boolean existsUidAndPowerId(@Param("roleUid") Long roleUid, @Param("powerUid") Long powerUid);

    // 插入角色权力
    int insertRolePower(RolePowerDto rolePowerDto);

    // 删除角色权力
    int deleteRolePower(Long uid);

    // 查询角色权力
    RolePowerEntity selectRolePower(Long uid);

    // 查询角色权力列表
    List<RolePowerEntity> selectRolePowerList(Integer scope);

    // 查询角色下所有权限UID
    List<Long> selectAllPermissionUid(Long roleUid);

    // 查询角色下所有批次UID
    List<Long> selectAllBatchUid(Long roleUid);

}
