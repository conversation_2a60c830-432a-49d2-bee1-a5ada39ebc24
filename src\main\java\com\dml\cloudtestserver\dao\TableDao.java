package com.dml.cloudtestserver.dao;


import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Mapper
@Repository
public interface TableDao {

    // 统计表总记录数
    int countRowsInTable(String tableName);

    // 重命名表
    void renameTable(@Param("oldTableName") String oldTableName, @Param("newTableName") String newTableName);

    // 表是否存在
    Integer existTable(String tableName);

    // 删除表
    void dropTable(String tableName);

    // 创建样片表
    void createSampleTable(String tableName);

    // 添加样片表索引
    void addSampleTableIndex(String tableName);
}
