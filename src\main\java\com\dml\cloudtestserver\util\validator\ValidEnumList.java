package com.dml.cloudtestserver.util.validator;


import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.METHOD, ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = EnumListValidator.class)
public @interface ValidEnumList {
    String message() default "不支持的枚举值";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    // 新增参数，用于指定校验的枚举类
    Class<? extends Enum<?>> enumClass();
}
