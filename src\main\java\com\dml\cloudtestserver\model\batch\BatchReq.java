package com.dml.cloudtestserver.model.batch;


import com.dml.cloudtestserver.util.constants.BatchStatusEnum;
import com.dml.cloudtestserver.util.constants.Constants;
import com.dml.cloudtestserver.util.validator.ValidEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;


/**
 * 批次表
 */
@Schema(description = "BatchReq 批次请求模型")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BatchReq implements Serializable {

    private static final long serialVersionUID = 4586348403722590278L;


    /**
     * 批次号
     */
    @Schema(description = "批次号。由中文、字符、数字、下划线、井号#、圆括号()组成，长度1-50")
    @NotNull(message = "批次号必填")
    @Length(min = 1, max = 50)
    @Pattern(regexp = Constants.REGEX_LETTER_NUMBER_UNDERLINE_WELL_PARENTHESES,
            message = Constants.REGEX_LETTER_NUMBER_UNDERLINE_WELL_PARENTHESES_MSG)
    private String batchId;

    /**
     * 预生产样片数量
     */
    @Schema(description = "预生产样片数量。")
    @NotNull(message = "预生产样片数量必填")
    @Min(value = 1, message = "预生产样片数量范围为1~1亿")
    @Max(value = 100000000, message = "预生产样片数量范围为1~1亿")
    private Integer preProductionSampleNum;

    /**
     * 批次状态：1-未开始，2-运行中，4-已停止
     */
    @Schema(description = "批次状态。可选值：1-未开始(默认)、2-运行中、4-已停止")
    @NotNull(message = "批次状态不能为空")
    @ValidEnum(enumClass = BatchStatusEnum.class, message = "可选值：1-未开始(默认)、2-运行中、4-已停止")
    private Integer status = BatchStatusEnum.UNSTARTED.getValue();
}