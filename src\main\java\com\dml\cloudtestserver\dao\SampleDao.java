package com.dml.cloudtestserver.dao;

import com.dml.cloudtestserver.model.sample.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Mapper
@Repository
public interface SampleDao {

    // SampleId是否存在
    boolean existsSampleBySampleId(
            @Param("tbSampleName") String tbSampleName,
            @Param("sampleId") String sampleId);

    // 插入样片记录
    int insertSample(
            @Param("tbSampleName") String tbSampleName,
            @Param("sampleInsertDto") SampleInsertDto sampleInsertDto);

    // 更新样片记录
    int updateSample(
            @Param("tbSampleName") String tbSampleName,
            @Param("sampleUpdateDto") SampleUpdateDto sampleUpdateDto);

    // 查询样片记录
    SampleEntity selectSample(
            @Param("tbSampleName") String tbSampleName,
            @Param("uid") Long uid);

    // 查询样片记录列表
    List<SampleEntity> selectSampleList(
            @Param("tbSampleName") String tbSampleName,
            @Param("sampleId") String sampleId,
            @Param("productType") List<Integer> productType,
            @Param("productionResult") List<Integer> productionResult,
            @Param("logUploadStatus") List<Integer> logUploadStatus,
            @Param("startCreatedTime") Date startCreatedTime,
            @Param("endCreatedTime") Date endCreatedTime);

    // 查询所有样片ID
    List<SampleIdRes> selectAllSampleId(String tbSampleName);

    // 统计指定表名中总样片数量
    int countSampleNum(String tbSampleName);

    // 统计当天每小时样片数
    List<PerHourSampleCountDto> countHourlySampleNum(String tbSampleName);

    // 统计当天每小时样片成功率
    List<PerHourSuccessRateDto> countHourlySampleSuccessRate(String tbSampleName);

    // 统计当天每种容量的样片数，容量以GB为单位
    List<PerCapacitySampleCountDto> countSampleNumByCapacity(String tbSampleName);

}


