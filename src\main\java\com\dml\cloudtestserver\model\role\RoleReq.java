package com.dml.cloudtestserver.model.role;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class RoleReq implements Serializable {

    private static final long serialVersionUID = -8054662166975616751L;

    /**
     * 角色名称
     */
    @Schema(description = "角色名称。由中文、字母、数字、下划线组成（下划线不能开头或结尾或仅有下划线），长度1-50")
    @NotBlank(message = "角色名称必填")
    @Length(min = 1, max = 50)
    private String name;
}
