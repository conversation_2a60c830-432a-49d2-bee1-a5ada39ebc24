//package com.dml.cloudtestserver.controller;
//
//
//import cn.dev33.satoken.annotation.SaCheckPermission;
//import com.dml.cloudtestserver.model.role.RoleReq;
//import com.dml.cloudtestserver.service.RoleService;
//import com.dml.cloudtestserver.util.constants.Constants;
//import com.dml.cloudtestserver.util.response.ResponseResult;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.media.Schema;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.*;
//
//import javax.validation.Valid;
//
//@Tag(name = "角色管理")
//@RestController
//@RequestMapping("/api/role")
//public class RoleController {
//
//    @Autowired
//    private RoleService roleService;
//
//
//    @Operation(summary = "添加角色")
//    @PostMapping("")
//    @SaCheckPermission("role-add")
//    public ResponseResult<?> addRole(
//            @Schema(implementation = RoleReq.class) @Valid @RequestBody RoleReq roleReq) {
//        return roleService.addRole(roleReq);
//    }
//
//
//    @Operation(summary = "删除角色")
//    @DeleteMapping("/{uid}")
//    @SaCheckPermission("role-delete")
//    public ResponseResult<?> deleteRole(@PathVariable Long uid) {
//        return roleService.deleteRole(uid);
//    }
//
//
//    @Operation(summary = "更改角色")
//    @PutMapping("/{uid}")
//    @SaCheckPermission("role-update")
//    public ResponseResult<?> updateBatch(
//            @PathVariable Long uid,
//            @Schema(implementation = RoleReq.class) @Valid @RequestBody RoleReq roleReq) {
//        return roleService.updateRole(uid, roleReq);
//    }
//
//
//    @Operation(summary = "查询单个角色")
//    @GetMapping("/{uid}")
//    @SaCheckPermission("role-query")
//    public ResponseResult<?> queryRole(@PathVariable Long uid) {
//        return roleService.queryRole(uid);
//    }
//
//
//    @Operation(summary = "批量查询角色")
//    @GetMapping("")
//    @SaCheckPermission("role-query")
//    public ResponseResult<?> queryAllRole(
//            @Parameter(description = "页码") @RequestParam(value = "pageNum", defaultValue = Constants.DEFAULT_PAGE_NUM) Integer pageNum,
//            @Parameter(description = "每页数量") @RequestParam(value = "pageSize", defaultValue = Constants.DEFAULT_PAGE_SIZE) Integer pageSize,
//            @Parameter(description = "角色名称") @RequestParam(value = "name", required = false) String name) {
//        return roleService.queryAllRole(pageNum, pageSize, name);
//    }
//
//}
