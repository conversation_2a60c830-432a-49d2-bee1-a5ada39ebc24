<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.dml</groupId>
    <artifactId>CloudTestServer</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>CloudTestServer</name>
    <description>CloudTestServer</description>
    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <spring-boot.version>2.6.13</spring-boot.version>
    </properties>
    <dependencies>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>



        <!-- Spring Web Starter 依赖，提供了Spring MVC功能 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>


        <!-- Spring Boot Test Starter 依赖，提供了单元测试支持 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>  <!-- 测试范围，仅在测试时有效 -->
        </dependency>


        <!-- 参数校验 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>


        <!-- Spring Boot DevTools 依赖，提供了自动重启应用的功能 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <scope>runtime</scope>  <!-- 运行时依赖，仅在运行时有效 -->
            <optional>true</optional> <!-- 可选依赖，如果客户端不需要此依赖，则可以省略 -->
        </dependency>


        <!-- Lombok 依赖，提供了自动化的getter、setter等代码生成 -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>  <!-- 可选依赖，如果客户端不需要此依赖，则可以省略 -->
        </dependency>


        <!-- hutool工具类 -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.8.29</version>
        </dependency>


        <!-- Sa-Token 权限认证 -->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-spring-boot-starter</artifactId>
            <version>1.34.0</version>
        </dependency>


        <!-- Sa-Token 整合 jwt -->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-jwt</artifactId>
            <version>1.34.0</version>
        </dependency>


        <!-- MyBatis Spring Boot Starter 用于集成MyBatis到Spring Boot中 -->
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>2.2.2</version>
        </dependency>


        <!-- MyBatis Spring Boot Starter Test 用于测试MyBatis集成的应用程序 -->
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter-test</artifactId>
            <version>2.2.2</version>
            <scope>test</scope> <!-- 仅在测试环境中使用 -->
        </dependency>


        <!-- Mybatis-PageHelper分页插件 -->
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
            <version>1.4.6</version>
        </dependency>


        <!-- Spring Boot Starter Data Redis 用于与Redis数据库交互 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>


        <!-- MySQL数据库连接器 -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
            <scope>runtime</scope> <!-- 运行时依赖，不需要打包进最终制品中 -->
        </dependency>


        <!-- Druid 数据库连接池 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
            <version>1.2.20</version>
        </dependency>


        <!-- SpringDoc API文档 -->
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-ui</artifactId>
            <version>1.7.0</version>
        </dependency>


        <!-- CIFS/SMB 网络协议库 -->
        <dependency>
            <groupId>jcifs</groupId>
            <artifactId>jcifs</artifactId>
            <version>1.3.17</version>
        </dependency>


        <!-- Apache Commons Net 库包含一系列网络实用工具和协议实现。 -->
        <!-- 支持的协议包括 Echo、Finger、FTP、NNTP、NTP、POP3 (S)、SMTP (S)、Telnet 和 Whois。 -->
        <!-- https://mvnrepository.com/artifact/commons-net/commons-net -->
        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId>
            <version>3.9.0</version>
        </dependency>


        <!-- WebSocket -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>


        <!-- https://mvnrepository.com/artifact/com.jcraft/jsch -->
        <dependency>
            <groupId>com.jcraft</groupId>
            <artifactId>jsch</artifactId>
            <version>0.1.55</version>
        </dependency>


    </dependencies>


    <!-- Dependency Management 配置，用于统一管理依赖的版本 -->
    <dependencyManagement>
        <dependencies>


            <!-- spring-boot-dependencies 依赖，用于统一 Spring Boot 项目中的所有依赖版本 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>  <!-- 类型为 pom，表明这是一个依赖管理文件 -->
                <scope>import</scope>  <!-- scope 为 import，表示导入这个依赖管理 -->
            </dependency>


        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>


            <!-- Maven 编译插件配置 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>1.8</source>  <!-- 指定源代码的 Java 版本 -->
                    <target>1.8</target>  <!-- 指定目标代码的 Java 版本 -->
                    <encoding>UTF-8</encoding>  <!-- 指定源代码的字符编码 -->
                </configuration>
            </plugin>


            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <configuration>
                    <!-- 指定主类，用于启动 Spring Boot 应用程序 -->
                    <mainClass>com.dml.cloudtestserver.CloudTestServerApplication</mainClass>

                    <!-- 是否跳过插件的执行，默认为 false，即不跳过 -->
                    <!-- 在部署时需要保持为 false，以确保插件正常执行 -->
                    <skip>false</skip>
                </configuration>
                <executions>
                    <execution>
                        <id>repackage</id>
                        <goals>
                            <!-- repackage 目标，用于重新打包 JAR 文件，使其成为可执行的 JAR 文件 -->
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>


        </plugins>
    </build>

</project>
