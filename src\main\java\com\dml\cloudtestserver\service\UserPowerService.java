package com.dml.cloudtestserver.service;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.IdUtil;
import com.dml.cloudtestserver.dao.BatchDao;
import com.dml.cloudtestserver.dao.PermissionDao;
import com.dml.cloudtestserver.dao.UserDao;
import com.dml.cloudtestserver.dao.UserPowerDao;
import com.dml.cloudtestserver.model.user.UserEntity;
import com.dml.cloudtestserver.model.user_power.*;
import com.dml.cloudtestserver.util.constants.PowerScopeEnum;
import com.dml.cloudtestserver.util.constants.UserCategoryEnum;
import com.dml.cloudtestserver.util.response.ResponseEnum;
import com.dml.cloudtestserver.util.response.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;


@Slf4j
@Service
public class UserPowerService {

    @Autowired
    private UserPowerDao userPowerDao;

    @Autowired
    private UserDao userDao;

    @Autowired
    private PermissionDao permissionDao;

    @Autowired
    private BatchDao batchDao;

    /**
     * 批量更新权力
     */
    public ResponseResult<?> updateUserPower(Long userUid, List<UserPowerReq> userPowerReqList) {
        // 判断用户是否存在
        if (!userDao.existsUid(userUid)) {
            return ResponseResult.error(ResponseEnum.DATA_NOT_EXISTS, "账号不存在");
        }

        // 从Token取出操作者的用户UID，判断是否为被操作者用户的创建者
        UserEntity userEntity = userDao.selectUser(userUid);
        Long oldCreateUserUid = userEntity.getCreateUserUid();
        Long createUserUid = StpUtil.getLoginIdAsLong();
        if (!createUserUid.equals(oldCreateUserUid)) {
            return ResponseResult.error(ResponseEnum.NOT_CREATOR);
        }

        // 删除用户所有权力
        userPowerDao.deleteUserPowerList(userUid);

        // 添加失败列表
        List<UserPowerErrorRes> errorPowerReqList = new ArrayList<>();
        List<UserPowerSuccessRes> successPowerReqList = new ArrayList<>();

        for (UserPowerReq userPowerReq : userPowerReqList) {
            Integer scope = userPowerReq.getScope();
            Long powerUid = userPowerReq.getPowerUid();

            if (scope.equals(PowerScopeEnum.PERMISSION.getValue())) {
                if (!permissionDao.existsUid(powerUid)) {
                    // 权限不存在
                    errorPowerReqList.add(new UserPowerErrorRes(scope, powerUid, "权限不存在"));
                } else {
                    Long uid = IdUtil.getSnowflakeNextId();
                    int affectedRows = userPowerDao.insertUserPower(
                            new UserPowerDto(uid, userUid, scope, powerUid));
                    if (affectedRows == 0) {
                        errorPowerReqList.add(new UserPowerErrorRes(scope, powerUid, "权限权力插入失败"));
                    } else {
                        successPowerReqList.add(new UserPowerSuccessRes(uid, scope, powerUid));
                    }
                }
            } else if (scope.equals(PowerScopeEnum.BATCH.getValue())) {
                if (!batchDao.existsUid(powerUid)) {
                    // 批次不存在
                    errorPowerReqList.add(new UserPowerErrorRes(scope, powerUid, "批次不存在"));
                } else {
                    Long uid = IdUtil.getSnowflakeNextId();
                    int affectedRows = userPowerDao.insertUserPower(
                            new UserPowerDto(uid, userUid, scope, powerUid));
                    if (affectedRows == 0) {
                        errorPowerReqList.add(new UserPowerErrorRes(scope, powerUid, "批次权力插入失败"));
                    } else {
                        successPowerReqList.add(new UserPowerSuccessRes(uid, scope, powerUid));
                    }
                }
            }
        }

        // 返回结果
        HashMap<String, Object> map = new HashMap<>();

        HashMap<String, Object> errorMap = new HashMap<>();
        errorMap.put("count", errorPowerReqList.size());
        errorMap.put("list", errorPowerReqList);
        map.put("error", errorMap);

        HashMap<String, Object> successMap = new HashMap<>();
        successMap.put("count", successPowerReqList.size());
        successMap.put("list", successPowerReqList);
        map.put("success", successMap);

        if (errorPowerReqList.isEmpty()) {
            return ResponseResult.success(ResponseEnum.UPDATE_SUCCESS, map);
        } else {
            return ResponseResult.error(ResponseEnum.UPDATE_ERROR, map);
        }
    }


    /**
     * 批量查询用户权力
     */
    public ResponseResult<?> queryUserAllPower(Long userUid, Integer scope) {
        UserEntity userEntity = userDao.selectUser(userUid);
        if (userEntity == null) {
            return ResponseResult.error(ResponseEnum.DATA_NOT_EXISTS, "账号不存在");
        }

        // 超级管理员、创建者可查询
        Long oldCreateUserUid = userEntity.getCreateUserUid();
        Long loginUid = StpUtil.getLoginIdAsLong();
        UserEntity loginUserEntity = userDao.selectUser(loginUid);

        if (!loginUserEntity.getCategory().equals(UserCategoryEnum.SUPER_ADMIN.getValue())) {
            if (!loginUid.equals(oldCreateUserUid)) {
                return ResponseResult.error(ResponseEnum.NOT_CREATOR);
            }
        }

        // 查询数据
        List<UserPowerEntity> userPowerEntityList = userPowerDao.selectUserAllPower(userUid, scope);

        // 返回结果
        return ResponseResult.success(ResponseEnum.QUERY_SUCCESS, userPowerEntityList);
    }

}
