package com.dml.cloudtestserver.model.permission;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 功能实体
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FuncEntity implements Serializable {

    private static final long serialVersionUID = 1718966335471573138L;

    /**
     * UID
     */
    private Long uid;

    /**
     * 权限名称
     */
    private String name;

    /**
     * 权限码
     */
    private String code;

}