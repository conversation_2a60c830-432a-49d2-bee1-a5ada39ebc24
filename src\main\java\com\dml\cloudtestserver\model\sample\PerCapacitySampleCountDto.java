package com.dml.cloudtestserver.model.sample;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PerCapacitySampleCountDto implements Serializable {
    private static final long serialVersionUID = -2325406212442888069L;

    /**
     * 容量
     */
    private String capacityRange;

    /**
     * 样片数量
     */
    private Integer sampleCount;
}
