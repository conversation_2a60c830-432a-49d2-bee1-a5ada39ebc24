package com.dml.cloudtestserver.model.batch;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class BatchDto implements Serializable {

    private static final long serialVersionUID = 2294078303608532572L;

    /**
     * UID
     */
    private Long uid;

    /**
     * 批次号
     */
    private String batchId;

    /**
     * 样片表名称
     */
    private String tbSampleName;

    /**
     * 预生产样片数量
     */
    private Integer preProductionSampleNum;

    /**
     * 批次状态：1-未开始，2-运行中，4-已停止
     */
    private Integer status;
}