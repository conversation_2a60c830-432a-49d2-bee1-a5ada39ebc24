package com.dml.cloudtestserver.util.auth;

import cn.dev33.satoken.stp.StpInterface;
import com.dml.cloudtestserver.dao.PermissionDao;
import com.dml.cloudtestserver.dao.UserDao;
import com.dml.cloudtestserver.dao.UserPowerDao;
import com.dml.cloudtestserver.model.permission.FuncEntity;
import com.dml.cloudtestserver.model.permission.PermissionEntity;
import com.dml.cloudtestserver.model.user.UserEntity;
import com.dml.cloudtestserver.util.constants.PermissionScopeEnum;
import com.dml.cloudtestserver.util.constants.UserCategoryEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 自定义权限验证接口扩展
 */
@Component    // 保证此类被SpringBoot扫描，完成Sa-Token的自定义权限验证扩展
public class StpInterfaceImpl implements StpInterface {


    @Autowired
    private UserDao userDao;

    @Autowired
    private UserPowerDao userPowerDao;

    @Autowired
    private PermissionDao permissionDao;


    /**
     * 返回一个账号所拥有的权限码集合
     */
    @Override
    public List<String> getPermissionList(Object loginId, String loginType) {
        Long userUid = Long.parseLong((String) loginId);

        // 检查用户是否存在
        if (!userDao.existsUid(userUid)) {
            return null; // 账号不存在，返回 null
        }

        // 查询用户资料
        UserEntity userEntity = userDao.selectUser(userUid);

        // 判断是否为超级管理员
        List<String> funcCodeList;

        if (userEntity.getCategory().equals(UserCategoryEnum.SUPER_ADMIN.getValue())) {
            // 直接获取所有功能码
            funcCodeList = permissionDao.selectFuncList().stream()
                    .map(FuncEntity::getCode)
                    .collect(Collectors.toList());
        } else {
            // 查询用户所有的权限UID
            List<Long> permissionUidList = userPowerDao.selectAllPermissionUid(userUid);

            // 如果没有权限UID，返回空列表
            if (permissionUidList == null || permissionUidList.isEmpty()) {
                return new ArrayList<>();
            }

            // 获取功能码集合
            funcCodeList = new ArrayList<>();
            for (Long permissionUid : permissionUidList) {
                PermissionEntity permission = permissionDao.selectPermission(permissionUid);
                if (permission != null && permission.getScope() == PermissionScopeEnum.FUNC.getValue()) {
                    funcCodeList.add(permission.getCode());
                }
            }
        }

        return funcCodeList;
    }


    /**
     * 返回一个账号所拥有的角色标识集合 (权限与角色可分开校验)
     */
    @Override
    public List<String> getRoleList(Object loginId, String loginType) {
        return new ArrayList<>();
    }


}
