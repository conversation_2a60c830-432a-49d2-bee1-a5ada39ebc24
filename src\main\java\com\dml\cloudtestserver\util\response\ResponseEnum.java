package com.dml.cloudtestserver.util.response;


import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ResponseEnum {

    // 成功
    SUCCESS(2001, "成功"),
    QUERY_SUCCESS(2002, "查询成功"),
    ADD_SUCCESS(2003, "添加成功"),
    DELETE_SUCCESS(2004, "删除成功"),
    UPDATE_SUCCESS(2005, "更新成功"),
    UPLOAD_SUCCESS(2006, "上传成功"),
    DOWNLOAD_SUCCESS(2007, "下载成功"),
    MERGE_SUCCESS(2008, "合并成功"),

    // 通用客户端错误码
    NO_TOKEN_ERROR(4001, "未提供token"),
    INVALID_TOKEN_ERROR(4002, "token无效"),
    TOKEN_TIMEOUT_ERROR(4003, "token已过期"),
    NOT_LOGIN_ERROR(4004, "当前会话未登录"),
    NO_PERMISSION_ERROR(4005, "无权限"),
    RESOURCE_NOT_EXISTS(4006, "URL资源不存在"),
    METHOD_ARGUMENT_TYPE_MISMATCH(4007, "参数类型不匹配"),
    METHOD_NOT_SUPPORTED(4008, "请求方法不支持"),
    MEDIA_TYPE_NOT_SUPPORTED(4009, "不支持的媒体类型"),
    REQUEST_BODY_EMPTY(4010, "请求体为空"),
    PARAMETER_VALIDATION_ERROR(4011, "参数校验错误"),
    DATA_ALREADY_EXISTS(4012, "数据已存在"),
    DATA_NOT_EXISTS(4013, "数据不存在"),
    DATA_NOT_CHANGE(4014, "数据未改变"),
    ADD_ERROR(4015, "添加失败"),
    QUERY_ERROR(4016, "查询失败"),
    DELETE_ERROR(4017, "删除失败"),
    UPDATE_ERROR(4018, "更新失败"),
    UPLOAD_ERROR(4019, "上传失败"),
    DOWNLOAD_ERROR(4020, "下载失败"),
    MERGE_ERROR(4021, "合并失败"),
    PAYLOAD_TOO_LARGE(4022, "文件上传大小超出限制"),

    // 业务错误码
    PASSWORD_ERROR(4501, "密码不正确"),
    ACCOUNT_DISABLE(4502, "账号已禁用"),
    NOT_CREATOR(4503, "非用户的创建者不允许操作"),
    CREATOR_NOT_EXISTS(4504, "创建者不存在"),
    ALREADY_UPLOAD(4505, "已上传"),
    NOT_UPLOAD(4506, "未上传"),
    ONLY_CREATE_LOWER_LEVEL_USER(4507, "只能创建级别更低的用户"),
    ONLY_OWNER_CAN_OPERATE(4508, "只能由账号拥有者操作"),
    KEY_ERROR(4509, "密钥错误"),
    FTP_CONNECT_ERROR(4510, "FTP服务器连接失败"),

    // 服务端错误码
    INTERNAL_SERVER_ERROR(5001, "服务器未知异常"),
    ;


    private final int code; // 状态码
    private final String msg; // 消息

    public String getName() {
        return this.name(); // 枚举名称
    }
}
