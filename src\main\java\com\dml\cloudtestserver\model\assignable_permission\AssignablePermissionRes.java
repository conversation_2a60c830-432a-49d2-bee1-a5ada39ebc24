package com.dml.cloudtestserver.model.assignable_permission;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AssignablePermissionRes implements Serializable {

    private static final long serialVersionUID = -7463603863859912622L;

    /**
     * UID
     */
    private Long uid;

    /**
     * 用户类别
     */
    private Integer userCategory;

    /**
     * 权限UID
     */
    private Long permissionUid;

    /**
     * 权限范围
     */
    private Integer scope;

    /**
     * 权限名称
     */
    private String name;

    /**
     * 父权限UID
     */
    private Long parentUid;

    /**
     * 权限码
     */
    private String code;

    /**
     * 排序权重
     */
    private Integer sortOrder;


}
