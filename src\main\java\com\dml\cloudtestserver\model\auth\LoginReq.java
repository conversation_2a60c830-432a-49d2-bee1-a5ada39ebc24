package com.dml.cloudtestserver.model.auth;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class LoginReq implements Serializable {

    private static final long serialVersionUID = -254694178184060391L;

    /**
     * 账号
     */
    @Schema(description = "账号")
    @NotBlank(message = "账号必填")
    private String account;

    
    /**
     * 密码
     */
    @Schema(description = "密码")
    @NotBlank(message = "密码必填")
    private String password;

}
