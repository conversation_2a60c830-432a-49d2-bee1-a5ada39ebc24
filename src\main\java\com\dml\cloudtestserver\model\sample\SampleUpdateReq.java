package com.dml.cloudtestserver.model.sample;


import com.dml.cloudtestserver.util.constants.UploadStatusEnum;
import com.dml.cloudtestserver.util.validator.ValidEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SampleUpdateReq implements Serializable {

    private static final long serialVersionUID = 2501996002550679131L;

    /**
     * 日志上传状态: 0-未开始 1-成功 2-失败
     */
    @Schema(description = "日志上传状态。可选值: 0-未开始 1-成功 2-失败")
    @NotNull(message = "日志上传状态不能为空")
    @ValidEnum(enumClass = UploadStatusEnum.class, message = "可选值: 0-未开始 1-成功 2-失败")
    private Integer logUploadStatus = 0;
}