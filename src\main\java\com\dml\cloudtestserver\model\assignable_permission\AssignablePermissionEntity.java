package com.dml.cloudtestserver.model.assignable_permission;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AssignablePermissionEntity implements Serializable {

    private static final long serialVersionUID = -3629520508359635876L;

    /**
     * UID
     */
    private Long uid;

    /**
     * 用户类别
     */
    private Integer userCategory;

    /**
     * 权限UID
     */
    private Long permissionUid;
}
