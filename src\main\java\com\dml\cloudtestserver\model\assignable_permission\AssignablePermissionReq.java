package com.dml.cloudtestserver.model.assignable_permission;

import com.dml.cloudtestserver.util.constants.UserCategoryEnum;
import com.dml.cloudtestserver.util.validator.ValidEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AssignablePermissionReq implements Serializable {

    private static final long serialVersionUID = 8315799868238370168L;

    /**
     * 用户类别
     */
    @Schema(description = "用户类别: 1-超级管理员, 2-普通用户")
    @NotNull(message = "用户类别必填")
    @ValidEnum(enumClass = UserCategoryEnum.class, message = "可选值: 1-超级管理员, 2-普通用户")
    private Integer userCategory;

    /**
     * 权限UID列表
     */
    @Schema(description = "权限UID列表")
    @NotNull(message = "权限UID列表必填")
    private List<Long> permissionUidList;

}
